"use client";

import { DashboardLayout } from "@/components/admin/DashboardLayout";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger
} from "@/components/ui/dropdown-menu";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { motion } from "framer-motion";
import { useState } from "react";
import {
  Search,
  MoreHorizontal,
  CheckCircle,
  XCircle,
  MessageSquare,
  ChevronDown,
  Eye,
  Reply,
  Trash2,
  AlertTriangle
} from "lucide-react";
import Link from "next/link";

interface Comment {
  id: string;
  author: {
    name: string;
    email: string;
    avatar?: string;
  };
  content: string;
  postTitle: string;
  postSlug: string;
  date: string;
  status: "approved" | "pending" | "spam";
}

export default function CommentsPage() {
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState<string | null>(null);

  // Sample data
  const comments: Comment[] = [
    {
      id: "1",
      author: {
        name: "John Doe",
        email: "<EMAIL>",
        avatar: "https://ui-avatars.com/api/?name=John+Doe&background=random"
      },
      content: "This is a great article! I learned a lot from it and will definitely be applying these techniques in my own projects.",
      postTitle: "Getting Started with Next.js 14",
      postSlug: "getting-started-with-nextjs-14",
      date: "2023-11-15 14:32",
      status: "approved"
    },
    {
      id: "2",
      author: {
        name: "Jane Smith",
        email: "<EMAIL>",
        avatar: "https://ui-avatars.com/api/?name=Jane+Smith&background=random"
      },
      content: "I have a question about the section on server components. Can you explain more about how they differ from client components?",
      postTitle: "Getting Started with Next.js 14",
      postSlug: "getting-started-with-nextjs-14",
      date: "2023-11-15 16:45",
      status: "approved"
    },
    {
      id: "3",
      author: {
        name: "Mike Johnson",
        email: "<EMAIL>",
        avatar: "https://ui-avatars.com/api/?name=Mike+Johnson&background=random"
      },
      content: "I found a typo in the third paragraph. It should be 'their' not 'there'.",
      postTitle: "The Power of TailwindCSS",
      postSlug: "the-power-of-tailwindcss",
      date: "2023-11-14 09:12",
      status: "pending"
    },
    {
      id: "4",
      author: {
        name: "Sarah Williams",
        email: "<EMAIL>",
        avatar: "https://ui-avatars.com/api/?name=Sarah+Williams&background=random"
      },
      content: "Check out my website at spamlink.com for more information!",
      postTitle: "Framer Motion Animation Guide",
      postSlug: "framer-motion-animation-guide",
      date: "2023-11-13 22:18",
      status: "spam"
    },
    {
      id: "5",
      author: {
        name: "David Brown",
        email: "<EMAIL>",
        avatar: "https://ui-avatars.com/api/?name=David+Brown&background=random"
      },
      content: "This tutorial was exactly what I needed. Thanks for putting it together!",
      postTitle: "Building a Blog with shadcn/ui",
      postSlug: "building-a-blog-with-shadcnui",
      date: "2023-11-12 11:05",
      status: "approved"
    },
    {
      id: "6",
      author: {
        name: "Lisa Chen",
        email: "<EMAIL>",
        avatar: "https://ui-avatars.com/api/?name=Lisa+Chen&background=random"
      },
      content: "I'm having trouble implementing the animation example. Could you provide more details on the configuration?",
      postTitle: "Framer Motion Animation Guide",
      postSlug: "framer-motion-animation-guide",
      date: "2023-11-11 15:30",
      status: "pending"
    }
  ];

  const filteredComments = comments.filter(comment => {
    const matchesSearch =
      comment.content.toLowerCase().includes(searchTerm.toLowerCase()) ||
      comment.author.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      comment.postTitle.toLowerCase().includes(searchTerm.toLowerCase());

    const matchesStatus = !statusFilter || comment.status === statusFilter;

    return matchesSearch && matchesStatus;
  });

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "approved":
        return <Badge className="bg-[rgb(var(--success))]">Approved</Badge>;
      case "pending":
        return <Badge variant="outline" className="text-[rgb(var(--warning))] border-[rgb(var(--warning))]">Pending</Badge>;
      case "spam":
        return <Badge variant="destructive">Spam</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  return (
    <DashboardLayout title="Comments" subtitle="Manage comments on your blog posts">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3 }}
      >
        <Card>
          <CardHeader>
            <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
              <div>
                <CardTitle>All Comments</CardTitle>
                <CardDescription>
                  {filteredComments.length} comment{filteredComments.length !== 1 ? 's' : ''}
                </CardDescription>
              </div>
              <div className="flex items-center gap-2">
                <div className="relative">
                  <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-adaptive-muted" />
                  <Input
                    type="search"
                    placeholder="Search comments..."
                    className="pl-8 w-[200px]"
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                  />
                </div>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <Tabs defaultValue="all" className="space-y-4">
              <TabsList>
                <TabsTrigger value="all" onClick={() => setStatusFilter(null)}>
                  All
                </TabsTrigger>
                <TabsTrigger value="approved" onClick={() => setStatusFilter("approved")}>
                  Approved
                </TabsTrigger>
                <TabsTrigger value="pending" onClick={() => setStatusFilter("pending")}>
                  Pending
                </TabsTrigger>
                <TabsTrigger value="spam" onClick={() => setStatusFilter("spam")}>
                  Spam
                </TabsTrigger>
              </TabsList>

              <TabsContent value="all" className="space-y-4">
                {filteredComments.length === 0 ? (
                  <div className="text-center py-8 text-adaptive-muted">
                    <MessageSquare className="h-12 w-12 mx-auto mb-2 opacity-20" />
                    <p>No comments found</p>
                  </div>
                ) : (
                  filteredComments.map((comment) => (
                    <CommentCard key={comment.id} comment={comment} />
                  ))
                )}
              </TabsContent>

              <TabsContent value="approved" className="space-y-4">
                {filteredComments.filter(c => c.status === "approved").length === 0 ? (
                  <div className="text-center py-8 text-adaptive-muted">
                    <MessageSquare className="h-12 w-12 mx-auto mb-2 opacity-20" />
                    <p>No approved comments</p>
                  </div>
                ) : (
                  filteredComments
                    .filter(c => c.status === "approved")
                    .map((comment) => (
                      <CommentCard key={comment.id} comment={comment} />
                    ))
                )}
              </TabsContent>

              <TabsContent value="pending" className="space-y-4">
                {filteredComments.filter(c => c.status === "pending").length === 0 ? (
                  <div className="text-center py-8 text-adaptive-muted">
                    <MessageSquare className="h-12 w-12 mx-auto mb-2 opacity-20" />
                    <p>No pending comments</p>
                  </div>
                ) : (
                  filteredComments
                    .filter(c => c.status === "pending")
                    .map((comment) => (
                      <CommentCard key={comment.id} comment={comment} />
                    ))
                )}
              </TabsContent>

              <TabsContent value="spam" className="space-y-4">
                {filteredComments.filter(c => c.status === "spam").length === 0 ? (
                  <div className="text-center py-8 text-adaptive-muted">
                    <MessageSquare className="h-12 w-12 mx-auto mb-2 opacity-20" />
                    <p>No spam comments</p>
                  </div>
                ) : (
                  filteredComments
                    .filter(c => c.status === "spam")
                    .map((comment) => (
                      <CommentCard key={comment.id} comment={comment} />
                    ))
                )}
              </TabsContent>
            </Tabs>
          </CardContent>
        </Card>
      </motion.div>
    </DashboardLayout>
  );
}

function CommentCard({ comment }: { comment: Comment }) {
  return (
    <Card className="overflow-hidden">
      <div className="flex items-start p-4 gap-4">
        <Avatar className="h-10 w-10">
          <AvatarImage src={comment.author.avatar} alt={comment.author.name} />
          <AvatarFallback>{comment.author.name.charAt(0)}</AvatarFallback>
        </Avatar>
        <div className="flex-1 min-w-0">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2">
            <div>
              <h3 className="font-medium text-adaptive">{comment.author.name}</h3>
              <p className="text-xs text-adaptive-muted">{comment.author.email}</p>
            </div>
            <div className="flex items-center gap-2">
              {getStatusBadge(comment.status)}
              <span className="text-xs text-adaptive-muted">{comment.date}</span>
            </div>
          </div>
          <p className="mt-2 text-adaptive">
            {comment.content}
          </p>
          <div className="mt-2 flex items-center gap-2 text-xs text-adaptive-muted">
            <span>On post:</span>
            <Link href={`/blog/${comment.postSlug}`} className="text-[rgb(var(--primary))] hover:underline">
              {comment.postTitle}
            </Link>
          </div>
        </div>
        <div>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="icon">
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuLabel>Actions</DropdownMenuLabel>
              {comment.status !== "approved" && (
                <DropdownMenuItem>
                  <CheckCircle className="mr-2 h-4 w-4 text-[rgb(var(--success))]" />
                  Approve
                </DropdownMenuItem>
              )}
              {comment.status !== "spam" && (
                <DropdownMenuItem>
                  <AlertTriangle className="mr-2 h-4 w-4 text-[rgb(var(--warning))]" />
                  Mark as Spam
                </DropdownMenuItem>
              )}
              <DropdownMenuItem>
                <Reply className="mr-2 h-4 w-4" />
                Reply
              </DropdownMenuItem>
              <DropdownMenuItem>
                <Eye className="mr-2 h-4 w-4" />
                View Post
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem className="text-[rgb(var(--destructive))]">
                <Trash2 className="mr-2 h-4 w-4" />
                Delete
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>
    </Card>
  );
}
