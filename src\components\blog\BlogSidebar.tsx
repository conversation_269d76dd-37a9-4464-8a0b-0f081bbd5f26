"use client";

import Link from "next/link";
import { usePathname, useRouter, useSearchParams } from "next/navigation";
import { cn } from "@/lib/utils";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { SubscribeForm } from "@/components/blog/SubscribeForm";
import {
  FileText,
  Tag,
  Calendar,
  User,
  Grid,
  Search,
  X,
  Menu,
  Home,
  Mail
} from "lucide-react";
import { useState, useEffect } from "react";
import { getCategories, getRecentPosts, getTags, searchPosts } from "@/services/blogService";
import { useDebounce } from "@/hooks/useDebounce";

interface BlogSidebarProps {
  onClose?: () => void;
}

export function BlogSidebar({ onClose }: BlogSidebarProps) {
  const pathname = usePathname();
  const router = useRouter();
  const searchParams = useSearchParams();
  const [searchQuery, setSearchQuery] = useState(searchParams.get("q") || "");
  const debouncedSearchQuery = useDebounce(searchQuery, 300);

  // Get real data from the blog service
  const categories = getCategories();
  const recentPosts = getRecentPosts(3);
  const tags = getTags();

  // Handle search
  useEffect(() => {
    if (debouncedSearchQuery) {
      // Create a new URLSearchParams object based on the current query
      const params = new URLSearchParams(searchParams.toString());

      // Set the search query parameter
      params.set("q", debouncedSearchQuery);

      // Update the URL with the new search query
      router.push(`/blog?${params.toString()}`);
    } else if (searchParams.has("q")) {
      // If the search query is empty but there's a query parameter, remove it
      const params = new URLSearchParams(searchParams.toString());
      params.delete("q");

      // Update the URL without the search query
      router.push(`/blog${params.toString() ? `?${params.toString()}` : ""}`);
    }
  }, [debouncedSearchQuery, router, searchParams]);

  return (
    <div className="flex flex-col h-full bg-adaptive-card">
      <div className="flex items-center justify-between p-4 border-b border-[rgb(var(--border))]">
        <Link href="/blog" className="flex items-center gap-2 cursor-pointer">
          <div className="bg-[rgb(var(--primary))] text-[rgb(var(--primary-foreground))] p-1.5 rounded hover:bg-[rgb(var(--primary))]/90 transition-colors">
            <FileText className="h-5 w-5" />
          </div>
        </Link>
        {onClose && (
          <Button variant="ghost" size="icon" onClick={onClose} className="lg:hidden cursor-pointer">
            <X className="h-5 w-5" />
          </Button>
        )}
      </div>

      <div className="flex-1 py-4 px-4 overflow-auto">
        <div className="mb-6">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-adaptive-muted" />
            <input
              type="search"
              placeholder="Search posts..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full pl-9 pr-4 py-2 bg-adaptive-muted/10 border border-[rgb(var(--border))] rounded-md text-adaptive placeholder:text-adaptive-muted focus:outline-none focus:ring-2 focus:ring-[rgb(var(--primary))]"
            />
          </div>
        </div>

        <div className="mb-6">
          <Card className="bg-[rgb(var(--primary))]/5 border-[rgb(var(--primary))]/20">
            <CardHeader className="pb-2">
              <CardTitle className="text-base flex items-center gap-2">
                <Mail className="h-4 w-4" />
                Subscribe to Newsletter
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-xs text-adaptive-muted mb-3">
                Get the latest posts and updates delivered to your inbox.
              </p>
              <SubscribeForm
                source="sidebar"
                buttonText="Subscribe"
                placeholder="Your email"
              />
            </CardContent>
          </Card>
        </div>

        <div className="mb-6">
          <h3 className="font-medium text-adaptive mb-3 flex items-center gap-2">
            <Grid className="h-4 w-4" />
            Categories
          </h3>
          <ul className="space-y-2">
            {categories.map((category) => (
              <li key={category.name}>
                <Link
                  href={`/blog?category=${encodeURIComponent(category.name)}`}
                  className={cn(
                    "flex items-center justify-between text-adaptive-muted hover:text-adaptive transition-colors",
                    searchParams.get('category') === category.name && "text-[rgb(var(--primary))] font-medium"
                  )}
                >
                  <span>{category.name}</span>
                  <span className="text-xs bg-adaptive-muted/20 px-2 py-0.5 rounded-full">
                    {category.count}
                  </span>
                </Link>
              </li>
            ))}
          </ul>
        </div>

        <div className="mb-6">
          <h3 className="font-medium text-adaptive mb-3 flex items-center gap-2">
            <Calendar className="h-4 w-4" />
            Recent Posts
          </h3>
          <ul className="space-y-3">
            {recentPosts.map((post) => (
              <li key={post.slug}>
                <Link
                  href={`/blog/${post.slug}`}
                  className={cn(
                    "block text-adaptive-muted hover:text-adaptive transition-colors line-clamp-2",
                    pathname === `/blog/${post.slug}` && "text-[rgb(var(--primary))] font-medium"
                  )}
                >
                  {post.title}
                </Link>
              </li>
            ))}
          </ul>
        </div>

        <div>
          <h3 className="font-medium text-adaptive mb-3 flex items-center gap-2">
            <Tag className="h-4 w-4" />
            Tags
          </h3>
          <div className="flex flex-wrap gap-2">
            {tags.map((tag) => (
              <Link
                key={tag.name}
                href={`/blog?tag=${encodeURIComponent(tag.name)}`}
                className={cn(
                  "inline-flex items-center text-xs px-2 py-1 rounded-full bg-adaptive-muted/20 text-adaptive-muted hover:bg-[rgb(var(--primary))]/10 hover:text-[rgb(var(--primary))] transition-colors",
                  searchParams.get('tag') === tag.name && "bg-[rgb(var(--primary))]/10 text-[rgb(var(--primary))]"
                )}
              >
                {tag.name} <span className="ml-1">({tag.count})</span>
              </Link>
            ))}
          </div>
        </div>
      </div>


    </div>
  );
}
