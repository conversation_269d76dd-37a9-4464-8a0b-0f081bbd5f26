"use client";

import { useState, useEffect } from "react";
import { useSearchParams } from "next/navigation";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON>le } from "@/components/ui/card";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Loader2, Search, User, FileText, Tool } from "lucide-react";
import { toast } from "@/hooks/use-toast";
import Link from "next/link";
import { format } from "date-fns";

interface SearchResult {
  _id: string;
  type: "user" | "blog" | "tool";
  name?: string;
  email?: string;
  title?: string;
  slug?: string;
  toolName?: string;
  toolId?: string;
  status?: string;
  createdAt?: string;
  timestamp?: string;
  authorId?: {
    name: string;
  };
}

interface SearchResponse {
  results: SearchResult[];
  pagination: {
    total: number;
    page: number;
    limit: number;
    totalPages: number;
  };
}

export default function SearchPage() {
  const searchParams = useSearchParams();
  const initialQuery = searchParams.get("q") || "";
  const [query, setQuery] = useState(initialQuery);
  const [activeTab, setActiveTab] = useState("all");
  const [results, setResults] = useState<SearchResult[]>([]);
  const [pagination, setPagination] = useState({
    total: 0,
    page: 1,
    limit: 10,
    totalPages: 0,
  });
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Perform search
  const performSearch = async (searchQuery: string, type: string, page: number) => {
    if (!searchQuery.trim()) {
      setResults([]);
      setPagination({
        total: 0,
        page: 1,
        limit: 10,
        totalPages: 0,
      });
      return;
    }

    try {
      setIsLoading(true);
      setError(null);

      const response = await fetch(
        `/api/search?q=${encodeURIComponent(searchQuery)}&type=${type}&page=${page}&limit=10`
      );

      if (!response.ok) {
        throw new Error(`Search failed: ${response.statusText}`);
      }

      const data: SearchResponse = await response.json();
      setResults(data.results);
      setPagination(data.pagination);
    } catch (err) {
      console.error("Search error:", err);
      setError("Failed to perform search");
      toast({
        title: "Search Error",
        description: "Failed to perform search. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Search when query, tab, or page changes
  useEffect(() => {
    performSearch(query, activeTab, pagination.page);
  }, [query, activeTab, pagination.page]);

  // Handle search form submission
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    performSearch(query, activeTab, 1);
  };

  // Handle pagination
  const handlePageChange = (newPage: number) => {
    if (newPage < 1 || newPage > pagination.totalPages) return;
    setPagination(prev => ({ ...prev, page: newPage }));
  };

  // Format date for display
  const formatDate = (dateString?: string) => {
    if (!dateString) return "Unknown date";
    try {
      return format(new Date(dateString), "MMM d, yyyy");
    } catch (e) {
      return dateString;
    }
  };

  return (
    <div className="container py-6 space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-3xl font-bold">Search</h1>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Search Results</CardTitle>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="flex gap-2 mb-6">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                type="text"
                placeholder="Search..."
                value={query}
                onChange={(e) => setQuery(e.target.value)}
                className="pl-10"
              />
            </div>
            <Button type="submit" disabled={isLoading}>
              {isLoading ? <Loader2 className="h-4 w-4 animate-spin mr-2" /> : <Search className="h-4 w-4 mr-2" />}
              Search
            </Button>
          </form>

          <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
            <TabsList>
              <TabsTrigger value="all">All</TabsTrigger>
              <TabsTrigger value="users">Users</TabsTrigger>
              <TabsTrigger value="blog">Blog Posts</TabsTrigger>
              <TabsTrigger value="tools">Tools</TabsTrigger>
            </TabsList>

            <TabsContent value={activeTab} className="space-y-4">
              {isLoading ? (
                <div className="flex justify-center py-8">
                  <Loader2 className="h-8 w-8 animate-spin text-primary" />
                </div>
              ) : error ? (
                <div className="text-center py-8 text-destructive">
                  <p>{error}</p>
                </div>
              ) : results.length === 0 ? (
                <div className="text-center py-8 text-muted-foreground">
                  <p>No results found</p>
                </div>
              ) : (
                <>
                  <div className="text-sm text-muted-foreground mb-4">
                    Found {pagination.total} results for "{query}"
                  </div>

                  <div className="space-y-4">
                    {results.map((result) => (
                      <Card key={`${result.type}-${result._id}`} className="overflow-hidden">
                        <div className="flex items-start p-4">
                          <div className="mr-4 mt-1">
                            {result.type === "user" && <User className="h-5 w-5 text-blue-500" />}
                            {result.type === "blog" && <FileText className="h-5 w-5 text-green-500" />}
                            {result.type === "tool" && <Tool className="h-5 w-5 text-purple-500" />}
                          </div>
                          <div className="flex-1 min-w-0">
                            <Link
                              href={
                                result.type === "user" ? `/admin/users/${result._id}` :
                                result.type === "blog" ? `/admin/blog/${result.slug}` :
                                `/admin/tools?id=${result.toolId}`
                              }
                              className="text-lg font-medium hover:underline"
                            >
                              {result.type === "user" && (result.name || result.email)}
                              {result.type === "blog" && result.title}
                              {result.type === "tool" && result.toolName}
                            </Link>

                            <div className="text-sm text-muted-foreground mt-1">
                              {result.type === "user" && (
                                <>
                                  <span className="capitalize">{result.role}</span> • {result.email}
                                </>
                              )}
                              {result.type === "blog" && (
                                <>
                                  By {result.authorId?.name || "Unknown"} •
                                  <span className="ml-1 capitalize">{result.status}</span> •
                                  {formatDate(result.createdAt)}
                                </>
                              )}
                              {result.type === "tool" && (
                                <>
                                  Used on {formatDate(result.timestamp)} •
                                  <span className="ml-1 capitalize">{result.status}</span>
                                </>
                              )}
                            </div>
                          </div>
                        </div>
                      </Card>
                    ))}
                  </div>

                  {/* Pagination */}
                  {pagination.totalPages > 1 && (
                    <div className="flex justify-center gap-2 mt-6">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handlePageChange(pagination.page - 1)}
                        disabled={pagination.page === 1}
                      >
                        Previous
                      </Button>

                      {Array.from({ length: pagination.totalPages }, (_, i) => i + 1)
                        .filter(page =>
                          page === 1 ||
                          page === pagination.totalPages ||
                          Math.abs(page - pagination.page) <= 1
                        )
                        .map((page, index, array) => {
                          // Add ellipsis
                          if (index > 0 && page - array[index - 1] > 1) {
                            return (
                              <span key={`ellipsis-${page}`} className="px-3 py-2">
                                ...
                              </span>
                            );
                          }

                          return (
                            <Button
                              key={page}
                              variant={pagination.page === page ? "default" : "outline"}
                              size="sm"
                              onClick={() => handlePageChange(page)}
                            >
                              {page}
                            </Button>
                          );
                        })}

                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handlePageChange(pagination.page + 1)}
                        disabled={pagination.page === pagination.totalPages}
                      >
                        Next
                      </Button>
                    </div>
                  )}
                </>
              )}
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  );
}
