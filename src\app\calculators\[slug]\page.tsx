import { notFound } from "next/navigation";
import { ALL_CALCULATORS } from "@/data/calculators";
import CalculatorLayout from "@/components/calculators/CalculatorLayout";
import CalculatorDialog from "@/components/calculators/CalculatorDialog";

type Props = {
  params: { slug: string };
};

export default function CalculatorPage({ params }: Props) {
  const calculator = ALL_CALCULATORS.find(calc => calc.id === params.slug);

  if (!calculator) {
    notFound();
  }

  return (
    <CalculatorLayout
      title={calculator.title}
      description={calculator.description}
      iconName={calculator.icon}
    >
      <CalculatorDialog calculator={calculator} isModal={false} />
    </CalculatorLayout>
  );
}
