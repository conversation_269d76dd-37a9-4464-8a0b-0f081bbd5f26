'use client';

import Link from "next/link";
import { motion } from "framer-motion";
import {
  FiFileText,
  FiFile,
  FiBarChart2,
  FiImage,
  FiArchive,
  FiEdit,
  FiSliders,
  FiPieChart,
  FiCamera,
  FiEye,
  FiCode,
  FiGitMerge,
  FiRefreshCw,
  FiGrid,
  FiArrowRight
} from "react-icons/fi";
import * as LucideIcons from "lucide-react";
import Header from "@/components/layout/Header";
import Footer from "@/components/layout/Footer";
import {ThemeToggle} from "@/components/DarkToggale";

import { useTheme } from "@/hooks/useTheme";
import { getPopularCalculators } from "@/data/calculators";
import HeroSection from "@/components/layout/hero/HeroSection";

export default function Home() {
  const { theme } = useTheme();

  // Function to render the appropriate icon based on the icon name
  const renderIcon = (iconName: string, className: string = "w-8 h-8") => {
    // Convert kebab-case to PascalCase for Lucide icons
    const pascalCaseName = iconName
      .split("-")
      .map(part => part.charAt(0).toUpperCase() + part.slice(1))
      .join("");

    // Get the icon component from Lucide
    const IconComponent = (LucideIcons as any)[pascalCaseName];

    if (IconComponent) {
      return <IconComponent className={className} />;
    }

    // Fallback to a default icon if the specified icon doesn't exist
    return <LucideIcons.Calculator className={className} />;
  };
  const tools = [
    {
      name: "PDF to Word",
      icon: <FiFileText className="w-8 h-8" />,
      path: "/tools/pdf-to-word",
      description: "Convert PDF to editable Word documents",
      color: "text-blue-500"
    },
    {
      name: "PDF to PowerPoint",
      icon: <FiSliders className="w-8 h-8" />,
      path: "/tools/pdf-to-powerpoint",
      description: "Convert PDF to PowerPoint presentations",
      color: "text-purple-500"
    },
    {
      name: "PDF to Excel",
      icon: <FiBarChart2 className="w-8 h-8" />,
      path: "/tools/pdf-to-excel",
      description: "Convert PDF to Excel spreadsheets",
      color: "text-green-500"
    },
    {
      name: "PDF to JPG",
      icon: <FiImage className="w-8 h-8" />,
      path: "/tools/pdf-to-jpg",
      description: "Convert PDF pages to JPG images",
      color: "text-yellow-500"
    },
    {
      name: "PDF to PDF/A",
      icon: <FiArchive className="w-8 h-8" />,
      path: "/tools/pdf-to-pdfa",
      description: "Convert PDF to PDF/A for long-term archiving",
      color: "text-red-500"
    },
    {
      name: "Word to PDF",
      icon: <FiEdit className="w-8 h-8" />,
      path: "/tools/word-to-pdf",
      description: "Convert Word documents to PDF",
      color: "text-blue-500"
    },
    {
      name: "PowerPoint to PDF",
      icon: <FiSliders className="w-8 h-8" />,
      path: "/tools/powerpoint-to-pdf",
      description: "Convert PowerPoint presentations to PDF",
      color: "text-purple-500"
    },
    {
      name: "Excel to PDF",
      icon: <FiPieChart className="w-8 h-8" />,
      path: "/tools/excel-to-pdf",
      description: "Convert Excel spreadsheets to PDF",
      color: "text-green-500"
    },
    {
      name: "JPG to PDF",
      icon: <FiCamera className="w-8 h-8" />,
      path: "/tools/jpg-to-pdf",
      description: "Convert JPG images to PDF",
      color: "text-yellow-500"
    },
    {
      name: "HTML to PDF",
      icon: <FiCode className="w-8 h-8" />,
      path: "/tools/html-to-pdf",
      description: "Convert HTML pages to PDF",
      color: "text-red-500"
    },
    {
      name: "Scan to PDF",
      icon: <FiCamera className="w-8 h-8" />,
      path: "/tools/scan-to-pdf",
      description: "Convert scanned documents to PDF",
      color: "text-blue-500"
    },
    {
      name: "OCR PDF",
      icon: <FiEye className="w-8 h-8" />,
      path: "/tools/ocr-pdf",
      description: "Make scanned PDFs searchable with OCR",
      color: "text-purple-500"
    },
    {
      name: "Compare PDF",
      icon: <FiGrid className="w-8 h-8" />,
      path: "/tools/compare-pdf",
      description: "Compare two PDF documents side by side",
      color: "text-green-500"
    },
    {
      name: "Merge PDF",
      icon: <FiGitMerge className="w-8 h-8" />,
      path: "/tools/merge-pdf",
      description: "Combine multiple PDFs into one document",
      color: "text-yellow-500"
    },
  ];

  const container = {
    hidden: { opacity: 0 },
    show: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const item = {
    hidden: { opacity: 0, y: 20 },
    show: { opacity: 1, y: 0 }
  };

  return (
    <main className="min-h-screen bg-background text-foreground transition-all duration-500 ease-in-out">
      <Header/>
      <section>

      <HeroSection/>
      </section>

      {/* <section className="py-16 bg-gradient-to-r from-blue-500 to-blue-700 text-white">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="container mx-auto px-4 text-center"
        >
          <h1 className="text-4xl md:text-5xl font-bold mb-4">All-in-one PDF Solution</h1>
          <p className="text-xl mb-8 max-w-2xl mx-auto">
            Every tool you need to work with PDFs, all in one place
          </p>
          <motion.div
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            <Link
              href="/tools"
              className="inline-block bg-white text-blue-600 px-6 py-3 rounded-lg font-medium hover:bg-gray-100 transition-all shadow-lg"
            >
              View All Tools
            </Link>
          </motion.div>
        </motion.div>
      <ThemeToggle/>

      // </section> */}



    {/* Popular PDF Tools Section */}
<section className="py-20 container mx-auto px-4 md:px-6 bg-background transition-all duration-500 ease-in-out border-t border-blue-100 rounded-t-3xl shadow-inner">
  <motion.h2
    initial={{ opacity: 0 }}
    animate={{ opacity: 1 }}
    transition={{ delay: 0.2 }}
    className="text-4xl font-extrabold text-center mb-14 text-foreground tracking-tight"
  >
    Popular PDF Tools
  </motion.h2>

  <motion.div
    variants={container}
    initial="hidden"
    animate="show"
    className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-8"
  >
    {tools.slice(0, 8).map((tool) => (
      <motion.div
        key={tool.name}
        variants={item}
        whileHover={{ y: -6 }}
      >
        <Link
          href={tool.path}
          className="group block bg-card border border-border rounded-2xl p-6 shadow-sm hover:shadow-xl transition-all h-full"
        >
          <div className={`${tool.color} w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4 transition-all group-hover:bg-gradient-to-r group-hover:from-blue-500 group-hover:to-purple-600 group-hover:text-white`}>
            {tool.icon}
          </div>
          <h3 className="text-lg font-semibold text-center mb-2 text-foreground group-hover:text-primary">
            {tool.name}
          </h3>
          <p className="text-sm text-center text-muted-foreground group-hover:text-foreground">
            {tool.description}
          </p>
        </Link>
      </motion.div>
    ))}
  </motion.div>

  <motion.div
    initial={{ opacity: 0, y: 20 }}
    animate={{ opacity: 1, y: 0 }}
    transition={{ delay: 0.6 }}
    className="mt-12 text-center"
  >
    <Link
      href="/tools"
      className="inline-flex items-center px-6 py-3 bg-primary text-primary-foreground rounded-full font-medium hover:bg-primary/90 transition-all shadow-md group"
    >
      <span>See More Tools</span>
      <FiArrowRight className="ml-2 group-hover:translate-x-1 transition-transform" />
    </Link>
  </motion.div>
</section>

{/* Top Calculators Section */}
<section className="py-20 container mx-auto px-4 md:px-6 bg-background transition-all duration-500 ease-in-out border-t border-blue-100 rounded-t-3xl shadow-inner">
  <motion.h2
    initial={{ opacity: 0 }}
    animate={{ opacity: 1 }}
    transition={{ delay: 0.2 }}
    className="text-4xl font-extrabold text-center mb-14 text-foreground tracking-tight"
  >
    Top Calculators
  </motion.h2>

  <motion.div
    variants={container}
    initial="hidden"
    animate="show"
    className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-8"
  >
    {getPopularCalculators(4).map((calculator) => (
      <motion.div
        key={calculator.id}
        variants={item}
        whileHover={{ y: -6 }}
      >
        <Link
          href={`/calculators/${calculator.id}`}
          className="group block bg-card border border-border rounded-2xl p-6 shadow-sm hover:shadow-xl transition-all h-full"
        >
          <div className="w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4 text-primary group-hover:text-white group-hover:bg-gradient-to-r group-hover:from-blue-500 group-hover:to-purple-600 transition-all">
            {renderIcon(calculator.icon)}
          </div>
          <h3 className="text-lg font-semibold text-center mb-2 text-foreground group-hover:text-primary">
            {calculator.title}
          </h3>
          <p className="text-sm text-center text-muted-foreground group-hover:text-foreground">
            {calculator.description}
          </p>
        </Link>
      </motion.div>
    ))}
  </motion.div>

  <motion.div
    initial={{ opacity: 0, y: 20 }}
    animate={{ opacity: 1, y: 0 }}
    transition={{ delay: 0.6 }}
    className="mt-12 text-center"
  >
    <Link
      href="/calculators"
      className="inline-flex items-center px-6 py-3 bg-primary text-primary-foreground rounded-full font-medium hover:bg-primary/90 transition-all shadow-md group"
    >
      <span>See More Calculators</span>
      <FiArrowRight className="ml-2 group-hover:translate-x-1 transition-transform" />
    </Link>
  </motion.div>
</section>


      <footer className="py-12 bg-primary text-primary-foreground transition-all duration-500 ease-in-out">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div>
              <h3 className="text-xl font-semibold mb-4">PDF Tools</h3>
              <p className="text-primary-foreground/80 transition-colors duration-500 ease-in-out">
                Your all-in-one solution for PDF management and conversion.
              </p>
            </div>

              <div>
            {/* Commented out section */}
             </div>
            <div>
              <h3 className="text-xl font-semibold mb-4">Legal</h3>
              <ul className="space-y-2">
                <li>
                  <Link
                    href="/privacy"
                    className="text-primary-foreground/80 hover:text-primary-foreground transition-all duration-300 ease-in-out"
                  >
                    Privacy Policy
                  </Link>
                </li>
                <li>
                  <Link
                    href="/terms"
                    className="text-primary-foreground/80 hover:text-primary-foreground transition-all duration-300 ease-in-out"
                  >
                    Terms of Service
                  </Link>
                </li>
              </ul>
            </div>
          </div>
          <div className="mt-8 pt-8 text-center border-t border-primary-foreground/20 text-primary-foreground/70 transition-colors duration-500 ease-in-out">
            <p>
              &copy; {new Date().getFullYear()} PDF Tools. All rights reserved.
            </p>
          </div>
        </div>
      </footer>
    </main>
  );
}