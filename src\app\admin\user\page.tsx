// import { ToolsTable } from '@/components/admin/UsersTable';
import { UsersTable } from "@/components/admin/UsersTable";
import { Button } from '@/components/ui/button';
import { getUsers } from '@/lib/action/users';
import { authOptions } from '@/lib/authOption';
import { getServerSession } from 'next-auth/next';
import Link from 'next/link';
import { redirect } from 'next/navigation';

export default async function UsersPage() {
  const session = await getServerSession(authOptions);
  if (!session?.user || session.user.role !== 'admin') redirect('/login');

  const users = await getUsers();

  return (
    <div className="p-6 space-y-4">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold">User Management</h1>
        <Button asChild>
          <Link href="/admin/users/new">Add User</Link>
        </Button>
      </div>
      <UsersTable  data={users} />
    </div>
  );
}