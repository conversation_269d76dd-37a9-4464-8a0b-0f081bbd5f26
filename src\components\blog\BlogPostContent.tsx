"use client";

import { useEffect } from "react";
import Link from "next/link";
import { motion, useScroll, useTransform } from "framer-motion";
import { CommentSection } from "@/components/blog/CommentSection";
import { SubscribeForm } from "@/components/blog/SubscribeForm";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Mail } from "lucide-react";

interface BlogPost {
  _id?: string;
  title: string;
  date: string;
  author: string;
  category: string;
  image: string;
  content: string;
  description?: string;
}

interface BlogPostContentProps {
  post: BlogPost;
  slug: string;
}

export function BlogPostContent({ post, slug }: BlogPostContentProps) {
  const { scrollYProgress } = useScroll();
  const opacity = useTransform(scrollYProgress, [0, 0.1], [1, 0]);
  const scale = useTransform(scrollYProgress, [0, 0.1], [1, 0.95]);

  useEffect(() => {
    // Smooth scroll to top when page loads
    window.scrollTo({
      top: 0,
      behavior: "smooth",
    });
  }, [slug]);

  return (
    <main className="flex-1">
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.5 }}
      >
        <div className="relative">
          <motion.div
            style={{ opacity, scale }}
            className="h-[40vh] relative"
          >
            <div
              className="absolute inset-0 bg-cover bg-center"
              style={{ backgroundImage: `url(${post.image})` }}
            >
              <div className="absolute inset-0 bg-black/50 flex items-center justify-center">
                <div className="container mx-auto px-4 text-center text-white">
                  <motion.h1
                    className="text-4xl md:text-5xl font-bold mb-4"
                    initial={{ y: 20, opacity: 0 }}
                    animate={{ y: 0, opacity: 1 }}
                    transition={{ delay: 0.2, duration: 0.5 }}
                  >
                    {post.title}
                  </motion.h1>
                </div>
              </div>
            </div>
          </motion.div>
        </div>

        <div className="container mx-auto px-4 py-8">
          <div className="mb-6">
            <motion.div
              initial={{ x: -20, opacity: 0 }}
              animate={{ x: 0, opacity: 1 }}
              transition={{ delay: 0.3, duration: 0.5 }}
            >
              <Link
                href="/blog"
                className="text-primary hover:text-primary/80 inline-flex items-center"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="h-4 w-4 mr-1"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M15 19l-7-7 7-7"
                  />
                </svg>
                Back to Blog
              </Link>
            </motion.div>
          </div>

          <motion.article
            className="bg-card text-card-foreground rounded-lg shadow-sm overflow-hidden max-w-4xl mx-auto"
            initial={{ y: 50, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            transition={{ delay: 0.4, duration: 0.8, type: "spring", stiffness: 100 }}
          >
            <motion.div
              initial={{ opacity: 0, scale: 1.05 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ delay: 0.5, duration: 0.7, type: "spring", damping: 20 }}
            >
              <img
                src={post.image}
                alt={post.title}
                className="w-full h-64 object-cover"
              />
            </motion.div>

            <div className="p-8">
              <motion.div
                className="flex items-center space-x-4 mb-4"
                initial={{ y: 20, opacity: 0 }}
                animate={{ y: 0, opacity: 1 }}
                transition={{ delay: 0.5, duration: 0.5 }}
              >
                <span className="px-3 py-1 bg-primary/10 text-primary rounded-full text-sm font-medium">
                  {post.category}
                </span>
                <span className="text-muted-foreground text-sm">{post.date}</span>
              </motion.div>

              <motion.h1
                className="text-3xl font-bold mb-4 text-foreground"
                initial={{ y: 20, opacity: 0 }}
                animate={{ y: 0, opacity: 1 }}
                transition={{ delay: 0.6, duration: 0.5 }}
              >
                {post.title}
              </motion.h1>

              <motion.div
                className="flex items-center mb-6"
                initial={{ y: 20, opacity: 0 }}
                animate={{ y: 0, opacity: 1 }}
                transition={{ delay: 0.7, duration: 0.5 }}
              >
                <div className="w-10 h-10 rounded-full bg-primary/20 flex items-center justify-center text-primary font-bold">
                  {post.author.charAt(0)}
                </div>
                <div className="ml-3">
                  <p className="text-sm font-medium text-foreground">By {post.author}</p>
                </div>
              </motion.div>

              <motion.div
                className="prose prose-lg max-w-none text-foreground prose-headings:text-foreground prose-a:text-primary"
                dangerouslySetInnerHTML={{ __html: post.content }}
                initial={{ y: 30, opacity: 0 }}
                animate={{ y: 0, opacity: 1 }}
                transition={{ delay: 0.8, duration: 0.5 }}
              />

              <motion.div
                className="mt-8 pt-6 border-t border-border"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ delay: 0.9, duration: 0.5 }}
              >
                <h3 className="text-lg font-semibold mb-4 text-foreground">Related Tools</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <motion.div
                    whileHover={{ scale: 1.02 }}
                    transition={{ type: "spring", stiffness: 400, damping: 10 }}
                  >
                    <Link
                      href="/tools/compress-pdf"
                      className="flex items-center p-4 border border-border rounded-lg hover:bg-muted transition-colors"
                    >
                      <div className="text-2xl mr-3">🗜️</div>
                      <div>
                        <h4 className="font-medium text-foreground">Compress PDF</h4>
                        <p className="text-sm text-muted-foreground">
                          Reduce PDF file size while maintaining quality
                        </p>
                      </div>
                    </Link>
                  </motion.div>
                  <motion.div
                    whileHover={{ scale: 1.02 }}
                    transition={{ type: "spring", stiffness: 400, damping: 10 }}
                  >
                    <Link
                      href="/tools/word-to-pdf"
                      className="flex items-center p-4 border border-border rounded-lg hover:bg-muted transition-colors"
                    >
                      <div className="text-2xl mr-3">📝</div>
                      <div>
                        <h4 className="font-medium text-foreground">Word to PDF</h4>
                        <p className="text-sm text-muted-foreground">
                          Convert Word documents to PDF
                        </p>
                      </div>
                    </Link>
                  </motion.div>
                </div>
              </motion.div>

              {/* Newsletter Subscription */}
              <motion.div
                className="mt-10"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 1.0, duration: 0.5 }}
              >
                <Card className="bg-primary/5 border-primary/20">
                  <CardHeader className="pb-2">
                    <CardTitle className="flex items-center gap-2">
                      <Mail className="h-5 w-5" />
                      Subscribe to Our Newsletter
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="mb-4 text-sm text-muted-foreground">
                      Get the latest articles, tools, and resources delivered to your inbox.
                    </p>
                    <SubscribeForm
                      source="blog"
                      tags={[post.category]}
                      showNameField={true}
                      buttonText="Subscribe"
                      placeholder="Enter your email address"
                    />
                  </CardContent>
                </Card>
              </motion.div>
            </div>
          </motion.article>

          {/* Comments Section */}
          <motion.div
            className="max-w-4xl mx-auto mt-12 px-8"
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 1.1, duration: 0.5 }}
          >
            <CommentSection postId={post._id || slug} postTitle={post.title} />
          </motion.div>
        </div>
      </motion.div>
    </main>
  );
}
