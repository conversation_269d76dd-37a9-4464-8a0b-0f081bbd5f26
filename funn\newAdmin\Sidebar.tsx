// import React, { useState } from "react";
// import { <PERSON> } from "react-router-dom";
// import { Button } from "@/components/ui/Button";
// import { Separator } from "@/components/ui/separator";
// import {
//   LayoutDashboard,
//   Users,
//   Wrench,
//   FileText,
//   Settings,
//   BarChart,
//   LogOut,
//   Menu,
//   X,
//   BookOpen,
// } from "lucide-react";

// interface SidebarProps {
//   onLogout?: () => void;
// }

// const Sidebar = ({ onLogout = () => {} }: SidebarProps) => {
//   const [isCollapsed, setIsCollapsed] = useState(false);
//   const [isMobileOpen, setIsMobileOpen] = useState(false);

//   const toggleSidebar = () => {
//     setIsCollapsed(!isCollapsed);
//   };

//   const toggleMobileSidebar = () => {
//     setIsMobileOpen(!isMobileOpen);
//   };

//   const navItems = [
//     { name: "Dashboard", icon: <LayoutDashboard size={20} />, path: "/admin" },
//     { name: "Users", icon: <Users size={20} />, path: "/admin/users" },
//     { name: "Tools", icon: <Wrench size={20} />, path: "/admin/tools" },
//     {
//       name: "Blog Posts",
//       icon: <BookOpen size={20} />,
//       path: "/admin/blogs",
//     },
//     {
//       name: "Analytics",
//       icon: <BarChart size={20} />,
//       path: "/admin/analytics",
//     },
//     {
//       name: "Settings",
//       icon: <Settings size={20} />,
//       path: "/admin/settings",
//     },
//   ];

//   const renderNavItems = () => {
//     return navItems.map((item) => {
//       if (item.disabled) {
//         return (
//           <div key={item.path} className="w-full opacity-50 cursor-not-allowed">
//             <Button
//               variant="ghost"
//               className={`w-full justify-start mb-1 ${isCollapsed ? "px-2" : "px-4"}`}
//               disabled
//             >
//               <span className="mr-2">{item.icon}</span>
//               {!isCollapsed && <span>{item.name}</span>}
//             </Button>
//           </div>
//         );
//       }

//       return (
//         <Link to={item.path} key={item.path} className="w-full">
//           <Button
//             variant="ghost"
//             className={`w-full justify-start mb-1 ${isCollapsed ? "px-2" : "px-4"}`}
//           >
//             <span className="mr-2">{item.icon}</span>
//             {!isCollapsed && <span>{item.name}</span>}
//           </Button>
//         </Link>
//       );
//     });
//   };

//   // Mobile menu button (only visible on small screens)
//   const mobileMenuButton = (
//     <Button
//       variant="ghost"
//       size="icon"
//       className="md:hidden fixed top-4 left-4 z-50"
//       onClick={toggleMobileSidebar}
//     >
//       {isMobileOpen ? <X size={24} /> : <Menu size={24} />}
//     </Button>
//   );

//   return (
//     <>
//       {mobileMenuButton}

//       <aside
//         className={`bg-background border-r h-screen flex flex-col transition-all duration-300 ${isCollapsed ? "w-16" : "w-64"} 
//         ${isMobileOpen ? "fixed inset-y-0 left-0 z-40" : "hidden md:flex"}`}
//       >
//         <div className="p-4 flex items-center justify-between">
//           {!isCollapsed && <div className="font-bold text-xl">Admin Panel</div>}
//           <Button
//             variant="ghost"
//             size="icon"
//             className="hidden md:flex"
//             onClick={toggleSidebar}
//           >
//             <Menu size={20} />
//           </Button>
//         </div>

//         <Separator />

//         <nav className="flex-1 overflow-y-auto p-2">{renderNavItems()}</nav>

//         <Separator />

//         <div className="p-4">
//           <Button
//             variant="outline"
//             className={`w-full justify-start ${isCollapsed ? "px-2" : "px-4"}`}
//             onClick={onLogout}
//           >
//             <LogOut size={20} className="mr-2" />
//             {!isCollapsed && <span>Logout</span>}
//           </Button>
//         </div>
//       </aside>
//     </>
//   );
// };

// export default Sidebar;
