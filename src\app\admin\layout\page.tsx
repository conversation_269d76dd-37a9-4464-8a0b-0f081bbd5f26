"use client";

import DashboardLayout from "@/components/admin/DashboardLayout";
import { <PERSON>, CardContent, CardDescription, <PERSON><PERSON>ooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Tabs, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Separator } from "@/components/ui/separator";
import { motion, Reorder, useDragControls } from "framer-motion";
import { useState } from "react";
import {
  GripVertical,
  Plus,
  Trash2,
  Settings,
  Eye,
  Save,
  Layout,
  Heading1,
  Heading2,
  Image as ImageIcon,
  FileText,
  ListOrdered,
  Columns,
  PanelLeft,
  PanelRight
} from "lucide-react";

interface Block {
  id: string;
  type: string;
  title: string;
  icon: React.ReactNode;
  settings?: Record<string, any>;
}

export default function LayoutPage() {
  const [activeTab, setActiveTab] = useState("homepage");
  const [blocks, setBlocks] = useState<Block[]>([
    {
      id: "hero",
      type: "hero",
      title: "Hero Section",
      icon: <Layout className="h-4 w-4" />,
      settings: {
        title: "Welcome to Our Blog",
        subtitle: "Discover the latest articles and insights",
        showButton: true,
        buttonText: "Explore",
        buttonLink: "/blog",
        backgroundImage: "/images/hero-bg.jpg"
      }
    },
    {
      id: "featured",
      type: "featured-posts",
      title: "Featured Posts",
      icon: <FileText className="h-4 w-4" />,
      settings: {
        title: "Featured Articles",
        postsCount: 3,
        showImages: true,
        showExcerpt: true
      }
    },
    {
      id: "categories",
      type: "categories",
      title: "Categories",
      icon: <ListOrdered className="h-4 w-4" />,
      settings: {
        title: "Browse by Category",
        layout: "grid",
        showPostCount: true
      }
    },
    {
      id: "newsletter",
      type: "newsletter",
      title: "Newsletter",
      icon: <PanelRight className="h-4 w-4" />,
      settings: {
        title: "Subscribe to Our Newsletter",
        description: "Get the latest posts delivered right to your inbox.",
        buttonText: "Subscribe"
      }
    }
  ]);
  const [selectedBlock, setSelectedBlock] = useState<Block | null>(null);
  const [availableBlocks] = useState<Block[]>([
    { id: "hero", type: "hero", title: "Hero Section", icon: <Layout className="h-4 w-4" /> },
    { id: "featured-posts", type: "featured-posts", title: "Featured Posts", icon: <FileText className="h-4 w-4" /> },
    { id: "latest-posts", type: "latest-posts", title: "Latest Posts", icon: <FileText className="h-4 w-4" /> },
    { id: "categories", type: "categories", title: "Categories", icon: <ListOrdered className="h-4 w-4" /> },
    { id: "heading", type: "heading", title: "Heading", icon: <Heading1 className="h-4 w-4" /> },
    { id: "subheading", type: "subheading", title: "Subheading", icon: <Heading2 className="h-4 w-4" /> },
    { id: "image", type: "image", title: "Image", icon: <ImageIcon className="h-4 w-4" /> },
    { id: "two-columns", type: "two-columns", title: "Two Columns", icon: <Columns className="h-4 w-4" /> },
    { id: "newsletter", type: "newsletter", title: "Newsletter", icon: <PanelRight className="h-4 w-4" /> },
    { id: "sidebar", type: "sidebar", title: "Sidebar", icon: <PanelLeft className="h-4 w-4" /> }
  ]);

  const addBlock = (block: Block) => {
    const newBlock = {
      ...block,
      id: `${block.type}-${Date.now()}`,
      settings: getDefaultSettings(block.type)
    };
    setBlocks([...blocks, newBlock]);
  };

  const removeBlock = (id: string) => {
    setBlocks(blocks.filter(block => block.id !== id));
    if (selectedBlock && selectedBlock.id === id) {
      setSelectedBlock(null);
    }
  };

  const getDefaultSettings = (type: string) => {
    switch (type) {
      case "hero":
        return {
          title: "Welcome to Our Blog",
          subtitle: "Discover the latest articles and insights",
          showButton: true,
          buttonText: "Explore",
          buttonLink: "/blog",
          backgroundImage: ""
        };
      case "featured-posts":
      case "latest-posts":
        return {
          title: type === "featured-posts" ? "Featured Articles" : "Latest Articles",
          postsCount: 3,
          showImages: true,
          showExcerpt: true
        };
      case "categories":
        return {
          title: "Browse by Category",
          layout: "grid",
          showPostCount: true
        };
      case "heading":
        return {
          text: "Your Heading Here",
          size: "large",
          alignment: "left"
        };
      case "subheading":
        return {
          text: "Your Subheading Here",
          size: "medium",
          alignment: "left"
        };
      case "image":
        return {
          src: "",
          alt: "Image description",
          caption: "",
          fullWidth: false
        };
      case "two-columns":
        return {
          leftColumnWidth: 50,
          rightColumnWidth: 50,
          gap: "medium"
        };
      case "newsletter":
        return {
          title: "Subscribe to Our Newsletter",
          description: "Get the latest posts delivered right to your inbox.",
          buttonText: "Subscribe"
        };
      case "sidebar":
        return {
          showCategories: true,
          showRecentPosts: true,
          showTags: true
        };
      default:
        return {};
    }
  };

  const updateBlockSettings = (id: string, settings: Record<string, any>) => {
    setBlocks(blocks.map(block =>
      block.id === id ? { ...block, settings: { ...block.settings, ...settings } } : block
    ));

    if (selectedBlock && selectedBlock.id === id) {
      setSelectedBlock({ ...selectedBlock, settings: { ...selectedBlock.settings, ...settings } });
    }
  };

  const renderSettingsForm = () => {
    if (!selectedBlock) return null;

    const { type, settings } = selectedBlock;

    switch (type) {
      case "hero":
        return (
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="title">Title</Label>
              <Input
                id="title"
                value={settings?.title || ""}
                onChange={(e) => updateBlockSettings(selectedBlock.id, { title: e.target.value })}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="subtitle">Subtitle</Label>
              <Input
                id="subtitle"
                value={settings?.subtitle || ""}
                onChange={(e) => updateBlockSettings(selectedBlock.id, { subtitle: e.target.value })}
              />
            </div>
            <div className="flex items-center space-x-2">
              <input
                type="checkbox"
                id="showButton"
                checked={settings?.showButton || false}
                onChange={(e) => updateBlockSettings(selectedBlock.id, { showButton: e.target.checked })}
              />
              <Label htmlFor="showButton">Show Button</Label>
            </div>
            {settings?.showButton && (
              <>
                <div className="space-y-2">
                  <Label htmlFor="buttonText">Button Text</Label>
                  <Input
                    id="buttonText"
                    value={settings?.buttonText || ""}
                    onChange={(e) => updateBlockSettings(selectedBlock.id, { buttonText: e.target.value })}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="buttonLink">Button Link</Label>
                  <Input
                    id="buttonLink"
                    value={settings?.buttonLink || ""}
                    onChange={(e) => updateBlockSettings(selectedBlock.id, { buttonLink: e.target.value })}
                  />
                </div>
              </>
            )}
          </div>
        );

      case "featured-posts":
      case "latest-posts":
        return (
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="title">Title</Label>
              <Input
                id="title"
                value={settings?.title || ""}
                onChange={(e) => updateBlockSettings(selectedBlock.id, { title: e.target.value })}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="postsCount">Number of Posts</Label>
              <Input
                id="postsCount"
                type="number"
                min="1"
                max="10"
                value={settings?.postsCount || 3}
                onChange={(e) => updateBlockSettings(selectedBlock.id, { postsCount: parseInt(e.target.value) })}
              />
            </div>
            <div className="flex items-center space-x-2">
              <input
                type="checkbox"
                id="showImages"
                checked={settings?.showImages || false}
                onChange={(e) => updateBlockSettings(selectedBlock.id, { showImages: e.target.checked })}
              />
              <Label htmlFor="showImages">Show Images</Label>
            </div>
            <div className="flex items-center space-x-2">
              <input
                type="checkbox"
                id="showExcerpt"
                checked={settings?.showExcerpt || false}
                onChange={(e) => updateBlockSettings(selectedBlock.id, { showExcerpt: e.target.checked })}
              />
              <Label htmlFor="showExcerpt">Show Excerpt</Label>
            </div>
          </div>
        );

      default:
        return (
          <div className="text-center py-4 text-adaptive-muted">
            <p>Settings for this block type are not yet implemented.</p>
          </div>
        );
    }
  };

  return (
    <DashboardLayout title="Layout Editor" subtitle="Customize your blog layout">
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <motion.div
          className="lg:col-span-2 space-y-6"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3 }}
        >
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle>Page Layout</CardTitle>
                <Tabs value={activeTab} onValueChange={setActiveTab}>
                  <TabsList>
                    <TabsTrigger value="homepage">Homepage</TabsTrigger>
                    <TabsTrigger value="blog">Blog Page</TabsTrigger>
                    <TabsTrigger value="post">Post Page</TabsTrigger>
                  </TabsList>
                </Tabs>
              </div>
              <CardDescription>
                Drag and drop blocks to customize your {activeTab} layout
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="border rounded-md p-4 min-h-[400px] bg-adaptive-muted/10">
                {blocks.length === 0 ? (
                  <div className="h-full flex items-center justify-center text-adaptive-muted">
                    <div className="text-center">
                      <Layout className="h-12 w-12 mx-auto mb-2 opacity-20" />
                      <p>Drag blocks here to build your layout</p>
                    </div>
                  </div>
                ) : (
                  <Reorder.Group axis="y" values={blocks} onReorder={setBlocks} className="space-y-2">
                    {blocks.map((block) => (
                      <Reorder.Item
                        key={block.id}
                        value={block}
                        className="cursor-move"
                      >
                        <div
                          className={`border rounded-md p-3 bg-adaptive-card flex items-center gap-3 ${
                            selectedBlock?.id === block.id ? 'ring-2 ring-[rgb(var(--primary))]' : ''
                          }`}
                          onClick={() => setSelectedBlock(block)}
                        >
                          <GripVertical className="h-5 w-5 text-adaptive-muted flex-shrink-0" />
                          <div className="flex items-center gap-2 flex-1">
                            {block.icon}
                            <span>{block.title}</span>
                          </div>
                          <Button
                            variant="ghost"
                            size="icon"
                            onClick={(e) => {
                              e.stopPropagation();
                              removeBlock(block.id);
                            }}
                          >
                            <Trash2 className="h-4 w-4 text-[rgb(var(--destructive))]" />
                          </Button>
                        </div>
                      </Reorder.Item>
                    ))}
                  </Reorder.Group>
                )}
              </div>
            </CardContent>
            <CardFooter className="flex justify-between">
              <Button variant="outline">
                <Eye className="mr-2 h-4 w-4" />
                Preview
              </Button>
              <Button>
                <Save className="mr-2 h-4 w-4" />
                Save Layout
              </Button>
            </CardFooter>
          </Card>
        </motion.div>

        <motion.div
          className="space-y-6"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: 0.1 }}
        >
          <Card>
            <CardHeader>
              <CardTitle>Available Blocks</CardTitle>
              <CardDescription>Drag these blocks to the layout</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                {availableBlocks.map((block) => (
                  <div
                    key={block.type}
                    className="border rounded-md p-3 bg-adaptive-card flex items-center gap-3 cursor-pointer hover:bg-adaptive-muted/10"
                    onClick={() => addBlock(block)}
                  >
                    <div className="flex items-center gap-2 flex-1">
                      {block.icon}
                      <span>{block.title}</span>
                    </div>
                    <Button
                      variant="ghost"
                      size="icon"
                      onClick={(e) => {
                        e.stopPropagation();
                        addBlock(block);
                      }}
                    >
                      <Plus className="h-4 w-4" />
                    </Button>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Block Settings</CardTitle>
              <CardDescription>
                {selectedBlock
                  ? `Configure settings for ${selectedBlock.title}`
                  : 'Select a block to configure its settings'}
              </CardDescription>
            </CardHeader>
            <CardContent>
              {selectedBlock ? (
                renderSettingsForm()
              ) : (
                <div className="text-center py-8 text-adaptive-muted">
                  <Settings className="h-12 w-12 mx-auto mb-2 opacity-20" />
                  <p>Select a block to edit its settings</p>
                </div>
              )}
            </CardContent>
          </Card>
        </motion.div>
      </div>
    </DashboardLayout>
  );
}

