'use client';

import Link from "next/link";
import { useTheme } from "@/hooks/useTheme";
import { toolConfigs } from "@/lib/tool-configs";
import ToolsHeader from "@/components/tools/ToolsHeader";
import EnhancedToolCard from "@/components/tools/EnhancedToolCard";
import { motion } from "framer-motion";
import { FiArrowRight, FiSearch, FiTool, FiFileText, FiDownload } from "react-icons/fi";
import { useState, useEffect } from "react";

export default function ToolsPage() {
  const { theme } = useTheme();
  const [searchQuery, setSearchQuery] = useState("");
  const [filteredTools, setFilteredTools] = useState<any[]>([]);
  const [filteredComingSoonTools, setFilteredComingSoonTools] = useState<any[]>([]);
  const [totalToolCount, setTotalToolCount] = useState(0);

  // We don't need to manually apply the theme here anymore
  // The useTheme hook now handles this globally

  // Define tool types for better type safety
  interface Tool {
    name: string;
    icon: string;
    path?: string;
    description: string;
  }

  // Define tools array
  const tools = [
    {
      name: "PDF to Word",
      icon: "📄",
      path: "/tools/pdf-to-word",
      description: "Convert PDF to editable Word documents",
    },
    {
      name: "PDF to PowerPoint",
      icon: "📊",
      path: "/tools/pdf-to-powerpoint",
      description: "Convert PDF to PowerPoint presentations",
    },
    {
      name: "PDF to Excel",
      icon: "📈",
      path: "/tools/pdf-to-excel",
      description: "Convert PDF to Excel spreadsheets",
    },
    {
      name: "PDF to JPG",
      icon: "🖼️",
      path: "/tools/pdf-to-jpg",
      description: "Convert PDF pages to JPG images",
    },
    {
      name: "PDF to PDF/A",
      icon: "📑",
      path: "/tools/pdf-to-pdfa",
      description: "Convert PDF to PDF/A for long-term archiving",
    },
    {
      name: "Word to PDF",
      icon: "📝",
      path: "/tools/word-to-pdf",
      description: "Convert Word documents to PDF",
    },
    {
      name: "PowerPoint to PDF",
      icon: "🎯",
      path: "/tools/powerpoint-to-pdf",
      description: "Convert PowerPoint presentations to PDF",
    },
    {
      name: "Excel to PDF",
      icon: "📊",
      path: "/tools/excel-to-pdf",
      description: "Convert Excel spreadsheets to PDF",
    },
    {
      name: "JPG to PDF",
      icon: "📸",
      path: "/tools/jpg-to-pdf",
      description: "Convert JPG images to PDF",
    },
    {
      name: "HTML to PDF",
      icon: "🌐",
      path: "/tools/html-to-pdf",
      description: "Convert HTML pages to PDF",
    },
    {
      name: "Merge PDF",
      icon: "🔗",
      path: "/tools/merge-pdf",
      description: "Combine multiple PDFs into one document",
    },
    {
      name: "Split PDF",
      icon: "✂️",
      path: "/tools/split-pdf",
      description: "Split a PDF into multiple documents",
    },
    {
      name: "Compress PDF",
      icon: "🗜️",
      path: "/tools/compress-pdf",
      description: "Reduce PDF file size while maintaining quality",
    },
    {
      name: "Rotate PDF",
      icon: "🔄",
      path: "/tools/rotate-pdf",
      description: "Rotate PDF pages to the correct orientation",
    },
  ];

  // Coming soon tools
  const comingSoonTools = [
    {
      name: "PDF to Text",
      icon: "📝",
      description: "Extract text from PDF documents",
    },
    {
      name: "PDF OCR",
      icon: "👁️",
      description: "Convert scanned PDFs to searchable text",
    },
    {
      name: "PDF Annotate",
      icon: "✏️",
      description: "Add notes, highlights, and comments to PDFs",
    },
    {
      name: "PDF Form Filler",
      icon: "📋",
      description: "Fill and save PDF forms easily",
    },
    {
      name: "PDF Watermark",
      icon: "💧",
      description: "Add text or image watermarks to PDFs",
    },
    {
      name: "PDF Password Protect",
      icon: "🔒",
      description: "Secure your PDFs with password protection",
    },
    {
      name: "PDF Repair",
      icon: "🔧",
      description: "Fix corrupted or damaged PDF files",
    },
    {
      name: "PDF Compare",
      icon: "🔍",
      description: "Compare two PDF documents side by side",
    },
  ];

  // Initialize filtered tools and calculate total tool count
  useEffect(() => {
    // Initialize filtered tools with all tools
    setFilteredTools(tools);
    setFilteredComingSoonTools(comingSoonTools);

    // Set the total count of all tools (available + coming soon)
    setTotalToolCount(tools.length + comingSoonTools.length);
  }, [tools, comingSoonTools]);

  // Filter tools based on search query
  useEffect(() => {
    if (searchQuery.trim() === '') {
      setFilteredTools(tools);
      setFilteredComingSoonTools(comingSoonTools);
    } else {
      const query = searchQuery.toLowerCase().trim();

      // Filter available tools
      const filtered = tools.filter(tool =>
        tool.name.toLowerCase().includes(query) ||
        tool.description.toLowerCase().includes(query)
      );
      setFilteredTools(filtered);

      // Filter coming soon tools
      const filteredComingSoon = comingSoonTools.filter(tool =>
        tool.name.toLowerCase().includes(query) ||
        tool.description.toLowerCase().includes(query)
      );
      setFilteredComingSoonTools(filteredComingSoon);
    }
  }, [searchQuery, tools, comingSoonTools]);

  return (
    <main className="min-h-screen bg-var-bg-primary text-var-text-primary transition-colors duration-300" style={{
      backgroundColor: 'var(--bg-primary)',
      color: 'var(--text-primary)'
    }}>
      <ToolsHeader />

      {/* Modern Hero Section */}
      <section className="relative overflow-hidden transition-colors duration-300" style={{
        backgroundColor: 'var(--bg-primary)'
      }}>
        {/* Background gradient */}
        <div className="absolute inset-0 bg-gradient-to-br from-blue-500/10 to-purple-500/5 dark:from-blue-900/20 dark:to-purple-900/10" />

        {/* Animated background shapes */}
        <div className="absolute inset-0 overflow-hidden opacity-20">
          <motion.div
            className="absolute top-20 left-10 w-64 h-64 rounded-full bg-blue-400 dark:bg-blue-600 blur-3xl"
            animate={{
              x: [0, 30, 0],
              y: [0, 20, 0],
              opacity: [0.3, 0.5, 0.3]
            }}
            transition={{
              repeat: Infinity,
              duration: 8,
              ease: "easeInOut"
            }}
          />
          <motion.div
            className="absolute bottom-20 right-10 w-72 h-72 rounded-full bg-purple-400 dark:bg-purple-600 blur-3xl"
            animate={{
              x: [0, -30, 0],
              y: [0, -20, 0],
              opacity: [0.3, 0.5, 0.3]
            }}
            transition={{
              repeat: Infinity,
              duration: 10,
              ease: "easeInOut"
            }}
          />
        </div>

        <div className="container mx-auto px-4 py-20 relative z-10">
          <div className="max-w-4xl mx-auto text-center">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
            >
              <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold mb-6 bg-clip-text text-transparent bg-gradient-to-r from-blue-600 to-purple-600 dark:from-blue-400 dark:to-purple-400">
                All PDF Tools
              </h1>
              <p className="text-xl md:text-2xl mb-8 text-gray-700 dark:text-gray-300">
                Powerful tools to manage, convert, and optimize your PDF documents
              </p>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              className="flex flex-wrap justify-center gap-4 mb-12"
            >
              <div className="flex items-center px-4 py-2 rounded-full bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300">
                <FiTool className="mr-2" /> {totalToolCount} PDF Tools
              </div>
              <div className="flex items-center px-4 py-2 rounded-full bg-purple-100 dark:bg-purple-900/30 text-purple-700 dark:text-purple-300">
                <FiFileText className="mr-2" /> Easy to Use
              </div>
              <div className="flex items-center px-4 py-2 rounded-full bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-300">
                <FiDownload className="mr-2" /> Fast Processing
              </div>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.5, delay: 0.4 }}
              className="relative max-w-2xl mx-auto mb-8"
            >
              <div className="flex items-center p-2 bg-white dark:bg-gray-800 rounded-full shadow-lg border border-gray-200 dark:border-gray-700">
                <FiSearch className="ml-3 mr-2 text-gray-400" size={20} />
                <input
                  type="text"
                  placeholder="Search for a tool..."
                  className="w-full py-2 px-3 bg-transparent border-none focus:outline-none text-gray-800 dark:text-gray-200"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
                <button
                  className="ml-2 bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-full transition-colors duration-200"
                  onClick={() => {
                    // Scroll to results section when search button is clicked
                    document.getElementById('tools-section')?.scrollIntoView({ behavior: 'smooth' });
                  }}
                >
                  Search
                </button>
              </div>
            </motion.div>
          </div>
        </div>

        {/* Wave divider */}
        <div className="absolute bottom-0 left-0 right-0">
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1440 120" className="w-full h-auto">
            <path
              fill="currentColor"
              fillOpacity="1"
              className="text-gray-50 dark:text-gray-800"
              d="M0,64L80,69.3C160,75,320,85,480,80C640,75,800,53,960,48C1120,43,1280,53,1360,58.7L1440,64L1440,120L1360,120C1280,120,1120,120,960,120C800,120,640,120,480,120C320,120,160,120,80,120L0,120Z"
            ></path>
          </svg>
        </div>
      </section>

      <section id="tools-section" className="py-16 transition-colors duration-300" style={{
        backgroundColor: 'var(--bg-secondary)'
      }}>
        <div className="container mx-auto px-4">
          <motion.h2
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            className="text-3xl font-bold mb-12 text-center"
            style={{ color: 'var(--text-primary)' }}
          >
            Our PDF Tools Collection
          </motion.h2>

          {searchQuery.trim() !== '' && (
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              className="mb-8 text-center"
            >
              <p style={{ color: 'var(--text-secondary)' }}>
                {filteredTools.length === 0 ? (
                  'No tools found matching your search.'
                ) : (
                  `Found ${filteredTools.length} tool${filteredTools.length === 1 ? '' : 's'} matching "${searchQuery}"`
                )}
              </p>
            </motion.div>
          )}

          <motion.div
            initial="hidden"
            animate="visible"
            variants={{
              hidden: { opacity: 0 },
              visible: {
                opacity: 1,
                transition: {
                  staggerChildren: 0.05,
                  delayChildren: 0.1
                }
              }
            }}
            className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6"
          >
            {filteredTools.map((tool, index) => (
              <EnhancedToolCard
                key={tool.name}
                name={tool.name}
                description={tool.description}
                icon={tool.icon}
                path={tool.path}
                index={index}
              />
            ))}
          </motion.div>

          {filteredComingSoonTools.length > 0 && (
            <>
              <motion.h2
                initial={{ opacity: 0, y: -10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.3 }}
                className="text-2xl font-bold mt-16 mb-8 text-center"
                style={{ color: 'var(--text-primary)' }}
              >
                Coming Soon
              </motion.h2>

              <motion.div
                initial="hidden"
                animate="visible"
                variants={{
                  hidden: { opacity: 0 },
                  visible: {
                    opacity: 1,
                    transition: {
                      staggerChildren: 0.05,
                      delayChildren: 0.4
                    }
                  }
                }}
                className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6"
              >
                {filteredComingSoonTools.map((tool, index) => (
                  <motion.div
                    key={tool.name}
                    initial={{ opacity: 0, y: 20, scale: 0.95 }}
                    animate={{ opacity: 1, y: 0, scale: 1 }}
                    transition={{
                      duration: 0.4,
                      delay: 0.05 * (index % 8),
                      ease: [0.25, 0.1, 0.25, 1.0]
                    }}
                    whileHover={{
                      y: -5,
                      transition: { duration: 0.2, ease: "easeOut" }
                    }}
                    className="rounded-xl border p-6 transition-all duration-300 flex flex-col items-center text-center relative overflow-hidden shadow-sm"
                    style={{
                      backgroundColor: theme === 'dark' ? 'var(--bg-secondary)' : 'white',
                      borderColor: theme === 'dark' ? '#374151' : '#e5e7eb',
                      color: 'var(--text-primary)'
                    }}
                  >
                    <div className="absolute inset-0 flex items-center justify-center backdrop-blur-[2px] z-10"
                      style={{
                        backgroundColor: theme === 'dark' ? 'rgba(17, 24, 39, 0.6)' : 'rgba(255, 255, 255, 0.6)'
                      }}
                    >
                      <motion.span
                        whileHover={{ scale: 1.05 }}
                        className="px-4 py-1.5 bg-blue-600 text-white rounded-full text-sm font-medium shadow-md"
                      >
                        Coming Soon
                      </motion.span>
                    </div>
                    <motion.div
                      className="text-4xl mb-4"
                      whileHover={{ scale: 1.2, rotate: 5 }}
                      transition={{ duration: 0.2 }}
                    >
                      {tool.icon}
                    </motion.div>
                    <h3 className="text-xl font-semibold mb-2" style={{ color: 'var(--text-primary)' }}>{tool.name}</h3>
                    <p style={{ color: 'var(--text-secondary)' }}>{tool.description}</p>
                  </motion.div>
                ))}
              </motion.div>
            </>
          )}
        </div>
      </section>

      <section className="py-12 transition-colors duration-300" style={{
        backgroundColor: 'var(--bg-primary)'
      }}>
        <div className="container mx-auto px-4">
          <motion.h2
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.5 }}
            className="text-2xl font-bold mb-10 text-center"
            style={{ color: 'var(--text-primary)' }}
          >
            Latest from Our Blog
          </motion.h2>
          <motion.div
            initial="hidden"
            animate="visible"
            variants={{
              hidden: { opacity: 0 },
              visible: {
                opacity: 1,
                transition: {
                  staggerChildren: 0.1,
                  delayChildren: 0.6
                }
              }
            }}
            className="grid grid-cols-1 md:grid-cols-3 gap-8"
          >
            {[
              {
                image: "https://images.unsplash.com/photo-1606857521015-7f9fcf423740?w=800&q=80",
                alt: "PDF Tips",
                title: "5 Tips for Efficient PDF Management",
                description: "Learn how to organize, compress, and manage your PDF documents effectively.",
                link: "/blog/pdf-management-tips"
              },
              {
                image: "https://images.unsplash.com/photo-1586281380349-632531db7ed4?w=800&q=80",
                alt: "PDF Conversion",
                title: "When to Convert Word to PDF",
                description: "Discover the advantages of converting your Word documents to PDF format.",
                link: "/blog/word-to-pdf-benefits"
              },
              {
                image: "https://images.unsplash.com/photo-1581291518633-83b4ebd1d83e?w=800&q=80",
                alt: "PDF Security",
                title: "Securing Your PDF Documents",
                description: "Learn about password protection, encryption, and other security features for PDFs.",
                link: "/blog/pdf-security-guide"
              }
            ].map((blog) => (
              <motion.div
                key={blog.title}
                variants={{
                  hidden: { opacity: 0, y: 20 },
                  visible: {
                    opacity: 1,
                    y: 0,
                    transition: {
                      duration: 0.4,
                      ease: [0.25, 0.1, 0.25, 1.0]
                    }
                  }
                }}
                whileHover={{
                  y: -8,
                  transition: { duration: 0.2 }
                }}
                className="rounded-xl border overflow-hidden shadow-sm hover:shadow-lg transition-all duration-300"
                style={{
                  backgroundColor: theme === 'dark' ? 'var(--bg-secondary)' : 'white',
                  borderColor: theme === 'dark' ? '#374151' : '#e5e7eb'
                }}
              >
                <div className="overflow-hidden">
                  <motion.img
                    whileHover={{ scale: 1.05 }}
                    transition={{ duration: 0.4 }}
                    src={blog.image}
                    alt={blog.alt}
                    className="w-full h-48 object-cover transition-transform duration-500"
                  />
                </div>
                <div className="p-6">
                  <h3 className="text-xl font-semibold mb-2" style={{ color: 'var(--text-primary)' }}>
                    {blog.title}
                  </h3>
                  <p className="mb-4" style={{ color: 'var(--text-secondary)' }}>
                    {blog.description}
                  </p>
                  <motion.div whileHover={{ x: 4 }} transition={{ duration: 0.2 }}>
                    <Link
                      href={blog.link}
                      className="font-medium inline-flex items-center text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300"
                    >
                      Read More <FiArrowRight className="ml-1" />
                    </Link>
                  </motion.div>
                </div>
              </motion.div>
            ))}
          </motion.div>
          <motion.div
            className="text-center mt-12"
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.4, delay: 0.8 }}
          >
            <motion.div
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.98 }}
              transition={{ duration: 0.2 }}
            >
              <Link
                href="/blog"
                className="bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white px-8 py-4 rounded-full font-medium transition-all duration-300 inline-flex items-center shadow-md hover:shadow-xl"
              >
                View All Articles
                <FiArrowRight className="ml-2" />
              </Link>
            </motion.div>
          </motion.div>
        </div>
      </section>

      <footer className="py-16 transition-colors duration-300" style={{
        backgroundColor: theme === 'dark' ? '#0f172a' : '#1e293b', // dark blue in dark mode, slightly lighter in light mode
        color: 'white'
      }}>
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-12">
            <div className="col-span-1 md:col-span-2">
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5 }}
              >
                <h3 className="text-2xl font-bold mb-6 text-white">PDF Tools</h3>
                <p className="text-gray-300 mb-6 max-w-md">
                  Your all-in-one solution for PDF management and conversion. We provide powerful tools to help you work with PDF documents efficiently.
                </p>
                <div className="flex space-x-4">
                  <a href="#" className="text-gray-300 hover:text-white transition-colors">
                    <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                      <path fillRule="evenodd" d="M22 12c0-5.523-4.477-10-10-10S2 6.477 2 12c0 4.991 3.657 9.128 8.438 9.878v-6.987h-2.54V12h2.54V9.797c0-2.506 1.492-3.89 3.777-3.89 1.094 0 2.238.195 2.238.195v2.46h-1.26c-1.243 0-1.63.771-1.63 1.562V12h2.773l-.443 2.89h-2.33v6.988C18.343 21.128 22 16.991 22 12z" clipRule="evenodd"></path>
                    </svg>
                  </a>
                  <a href="#" className="text-gray-300 hover:text-white transition-colors">
                    <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                      <path d="M8.29 20.251c7.547 0 11.675-6.253 11.675-11.675 0-.178 0-.355-.012-.53A8.348 8.348 0 0022 5.92a8.19 8.19 0 01-2.357.646 4.118 4.118 0 001.804-2.27 8.224 8.224 0 01-2.605.996 4.107 4.107 0 00-6.993 3.743 11.65 11.65 0 01-8.457-4.287 4.106 4.106 0 001.27 5.477A4.072 4.072 0 012.8 9.713v.052a4.105 4.105 0 003.292 4.022 4.095 4.095 0 01-1.853.07 4.108 4.108 0 003.834 2.85A8.233 8.233 0 012 18.407a11.616 11.616 0 006.29 1.84"></path>
                    </svg>
                  </a>
                  <a href="#" className="text-gray-300 hover:text-white transition-colors">
                    <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                      <path fillRule="evenodd" d="M12.315 2c2.43 0 2.784.013 3.808.06 1.064.049 1.791.218 2.427.465a4.902 4.902 0 011.772 1.153 4.902 4.902 0 011.153 1.772c.247.636.416 1.363.465 2.427.048 1.067.06 1.407.06 4.123v.08c0 2.643-.012 2.987-.06 4.043-.049 1.064-.218 1.791-.465 2.427a4.902 4.902 0 01-1.153 1.772 4.902 4.902 0 01-1.772 1.153c-.636.247-1.363.416-2.427.465-1.067.048-1.407.06-4.123.06h-.08c-2.643 0-2.987-.012-4.043-.06-1.064-.049-1.791-.218-2.427-.465a4.902 4.902 0 01-1.772-1.153 4.902 4.902 0 01-1.153-1.772c-.247-.636-.416-1.363-.465-2.427-.047-1.024-.06-1.379-.06-3.808v-.63c0-2.43.013-2.784.06-3.808.049-1.064.218-1.791.465-2.427a4.902 4.902 0 011.153-1.772A4.902 4.902 0 015.45 2.525c.636-.247 1.363-.416 2.427-.465C8.901 2.013 9.256 2 11.685 2h.63zm-.081 1.802h-.468c-2.456 0-2.784.011-3.807.058-.975.045-1.504.207-1.857.344-.467.182-.8.398-1.15.748-.35.35-.566.683-.748 1.15-.137.353-.3.882-.344 1.857-.047 1.023-.058 1.351-.058 3.807v.468c0 2.456.011 2.784.058 3.807.045.975.207 1.504.344 1.857.182.466.399.8.748 1.15.35.35.683.566 1.15.748.353.137.882.3 1.857.344 1.054.048 1.37.058 4.041.058h.08c2.597 0 2.917-.01 3.96-.058.976-.045 1.505-.207 1.858-.344.466-.182.8-.398 1.15-.748.35-.35.566-.683.748-1.15.137-.353.3-.882.344-1.857.048-1.055.058-1.37.058-4.041v-.08c0-2.597-.01-2.917-.058-3.96-.045-.976-.207-1.505-.344-1.858a3.097 3.097 0 00-.748-1.15 3.098 3.098 0 00-1.15-.748c-.353-.137-.882-.3-1.857-.344-1.023-.047-1.351-.058-3.807-.058zM12 6.865a5.135 5.135 0 110 10.27 5.135 5.135 0 010-10.27zm0 1.802a3.333 3.333 0 100 6.666 3.333 3.333 0 000-6.666zm5.338-3.205a1.2 1.2 0 110 2.4 1.2 1.2 0 010-2.4z" clipRule="evenodd"></path>
                    </svg>
                  </a>
                </div>
              </motion.div>
            </div>
            <div>
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.1 }}
              >
                <h3 className="text-lg font-semibold mb-4 text-white">Quick Links</h3>
                <ul className="space-y-3">
                  <li>
                    <Link href="/" className="text-gray-300 hover:text-white transition-colors duration-200 flex items-center">
                      <FiArrowRight className="mr-2 h-4 w-4" /> Home
                    </Link>
                  </li>
                  <li>
                    <Link href="/tools" className="text-gray-300 hover:text-white transition-colors duration-200 flex items-center">
                      <FiArrowRight className="mr-2 h-4 w-4" /> All Tools
                    </Link>
                  </li>
                  <li>
                    <Link href="/blog" className="text-gray-300 hover:text-white transition-colors duration-200 flex items-center">
                      <FiArrowRight className="mr-2 h-4 w-4" /> Blog
                    </Link>
                  </li>
                  <li>
                    <Link href="/contact" className="text-gray-300 hover:text-white transition-colors duration-200 flex items-center">
                      <FiArrowRight className="mr-2 h-4 w-4" /> Contact
                    </Link>
                  </li>
                </ul>
              </motion.div>
            </div>
            <div>
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.2 }}
              >
                <h3 className="text-lg font-semibold mb-4 text-white">Legal</h3>
                <ul className="space-y-3">
                  <li>
                    <Link href="/privacy" className="text-gray-300 hover:text-white transition-colors duration-200 flex items-center">
                      <FiArrowRight className="mr-2 h-4 w-4" /> Privacy Policy
                    </Link>
                  </li>
                  <li>
                    <Link href="/terms" className="text-gray-300 hover:text-white transition-colors duration-200 flex items-center">
                      <FiArrowRight className="mr-2 h-4 w-4" /> Terms of Service
                    </Link>
                  </li>
                  <li>
                    <Link href="/cookies" className="text-gray-300 hover:text-white transition-colors duration-200 flex items-center">
                      <FiArrowRight className="mr-2 h-4 w-4" /> Cookie Policy
                    </Link>
                  </li>
                </ul>
              </motion.div>
            </div>
          </div>
          <div className="border-t border-gray-700/50 mt-12 pt-8 text-center">
            <p className="text-gray-400">
              &copy; {new Date().getFullYear()} PDF Tools. All rights reserved.
            </p>
          </div>
        </div>
      </footer>
    </main>
  );
}
