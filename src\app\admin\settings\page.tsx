"use client";

import { useState } from "react";
import DashboardLayout from "@/components/admin/DashboardLayout";
import { Card, CardContent, CardDescription, CardHeader, CardT<PERSON>le, CardFooter } from "@/components/ui/card";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Switch } from "@/components/ui/switch";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import { motion } from "framer-motion";
import { toast } from "@/hooks/use-toast";
import { Save, Tag, Trash2, Bell } from "lucide-react";

export default function SettingsPage() {
  const [activeTab, setActiveTab] = useState("general");
  const [isSaving, setIsSaving] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  // General settings
  const [siteName, setSiteName] = useState("PDF Tools");
  const [siteDescription, setSiteDescription] = useState("All-in-one PDF solution for your document needs");
  const [siteUrl, setSiteUrl] = useState("https://pdf-tools.example.com");
  const [maintenanceMode, setMaintenanceMode] = useState(false);
  const [maintenanceMessage, setMaintenanceMessage] = useState("We're currently performing maintenance. Please check back soon.");

  // SEO settings
  const [metaTitle, setMetaTitle] = useState("PDF Tools - All-in-one PDF Solution");
  const [metaDescription, setMetaDescription] = useState("Free online PDF tools to merge, compress, convert, and edit PDF files");
  const [metaKeywords, setMetaKeywords] = useState<string[]>(["PDF tools", "convert PDF", "edit PDF", "merge PDF", "compress PDF"]);
  const [keywordInput, setKeywordInput] = useState("");

  // Social media & OG settings
  const [ogTitle, setOgTitle] = useState("PDF Tools - All-in-one PDF Solution");
  const [ogDescription, setOgDescription] = useState("Free online PDF tools to merge, compress, convert, and edit PDF files");
  const [ogImage, setOgImage] = useState("/og-image.jpg");
  const [twitterHandle, setTwitterHandle] = useState("@pdftools");

  // Logo & Favicon
  const [logoUrl, setLogoUrl] = useState("/logo.png");
  const [faviconUrl, setFaviconUrl] = useState("/favicon.ico");

  // Analytics & Tracking
  const [googleAnalyticsId, setGoogleAnalyticsId] = useState("");
  const [facebookPixelId, setFacebookPixelId] = useState("");

  // Notification settings
  const [emailNotifications, setEmailNotifications] = useState(true);

  // Add keyword to the list
  const addKeyword = () => {
    if (keywordInput.trim() && !metaKeywords.includes(keywordInput.trim())) {
      setMetaKeywords([...metaKeywords, keywordInput.trim()]);
      setKeywordInput("");
    }
  };

  // Remove keyword from the list
  const removeKeyword = (keyword: string) => {
    setMetaKeywords(metaKeywords.filter(k => k !== keyword));
  };

  // Handle save settings
  const handleSaveSettings = async () => {
    try {
      setIsSaving(true);

      const settings = {
        // General settings
        siteName,
        siteDescription,
        siteUrl,
        maintenanceMode,
        maintenanceMessage,

        // SEO settings
        metaTitle,
        metaDescription,
        metaKeywords,

        // Social media & OG settings
        ogTitle,
        ogDescription,
        ogImage,
        twitterHandle,

        // Logo & Favicon
        logoUrl,
        faviconUrl,

        // Analytics & Tracking
        googleAnalyticsId,
        facebookPixelId,
      };

      // Simulate API call for now
      await new Promise(resolve => setTimeout(resolve, 1000));

      // In a real app, you would send this to your API
      // const response = await fetch('/api/settings', {
      //   method: 'PUT',
      //   headers: {
      //     'Content-Type': 'application/json',
      //   },
      //   body: JSON.stringify(settings),
      // });

      // if (!response.ok) {
      //   throw new Error(`Failed to save settings: ${response.status}`);
      // }

      toast({
        title: "Settings saved",
        description: "Your site settings have been updated successfully."
      });
    } catch (error) {
      console.error("Error saving settings:", error);
      toast({
        title: "Error saving settings",
        description: error instanceof Error ? error.message : "An unknown error occurred",
        variant: "destructive"
      });
    } finally {
      setIsSaving(false);
    }
  };

  // Handle save notification settings
  const handleSaveNotifications = async () => {
    try {
      setIsSaving(true);

      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));

      toast({
        title: "Notification settings saved",
        description: "Your notification preferences have been updated."
      });
    } catch (error) {
      console.error("Error saving notification settings:", error);
      toast({
        title: "Error saving settings",
        description: error instanceof Error ? error.message : "An unknown error occurred",
        variant: "destructive"
      });
    } finally {
      setIsSaving(false);
    }
  };

  return (
    <DashboardLayout>
      <div className="space-y-6">
        <Tabs defaultValue="general" value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid grid-cols-4 md:grid-cols-5 lg:grid-cols-6">
            <TabsTrigger value="general">General</TabsTrigger>
            <TabsTrigger value="seo">SEO</TabsTrigger>
            <TabsTrigger value="social">Social Media</TabsTrigger>
            <TabsTrigger value="appearance">Appearance</TabsTrigger>
            <TabsTrigger value="analytics">Analytics</TabsTrigger>
            <TabsTrigger value="notifications">Notifications</TabsTrigger>
          </TabsList>

          <TabsContent value="general" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Site Information</CardTitle>
                <CardDescription>Basic information about your site</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid gap-2">
                  <Label htmlFor="siteName">Site Name</Label>
                  <Input
                    id="siteName"
                    value={siteName}
                    onChange={(e) => setSiteName(e.target.value)}
                    placeholder="Enter site name"
                  />
                </div>
                <div className="grid gap-2">
                  <Label htmlFor="siteDescription">Site Description</Label>
                  <Textarea
                    id="siteDescription"
                    value={siteDescription}
                    onChange={(e) => setSiteDescription(e.target.value)}
                    placeholder="Enter site description"
                    rows={3}
                  />
                </div>
                <div className="grid gap-2">
                  <Label htmlFor="siteUrl">Site URL</Label>
                  <Input
                    id="siteUrl"
                    value={siteUrl}
                    onChange={(e) => setSiteUrl(e.target.value)}
                    placeholder="https://example.com"
                  />
                </div>
              </CardContent>
              <CardFooter>
                <Button
                  onClick={handleSaveSettings}
                  disabled={isSaving}
                >
                  {isSaving ? (
                    <>
                      <motion.div
                        className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent"
                      />
                      Saving...
                    </>
                  ) : (
                    <>
                      <Save className="mr-2 h-4 w-4" />
                      Save Changes
                    </>
                  )}
                </Button>
              </CardFooter>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Maintenance Mode</CardTitle>
                <CardDescription>Enable maintenance mode to temporarily disable your site</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center space-x-2">
                  <Switch
                    id="maintenanceMode"
                    checked={maintenanceMode}
                    onCheckedChange={setMaintenanceMode}
                  />
                  <Label htmlFor="maintenanceMode">Enable Maintenance Mode</Label>
                </div>
                {maintenanceMode && (
                  <div className="grid gap-2">
                    <Label htmlFor="maintenanceMessage">Maintenance Message</Label>
                    <Textarea
                      id="maintenanceMessage"
                      value={maintenanceMessage}
                      onChange={(e) => setMaintenanceMessage(e.target.value)}
                      placeholder="Enter maintenance message"
                      rows={3}
                    />
                  </div>
                )}
              </CardContent>
              <CardFooter>
                <Button
                  onClick={handleSaveSettings}
                  disabled={isSaving}
                >
                  {isSaving ? (
                    <>
                      <motion.div
                        className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent"
                      />
                      Saving...
                    </>
                  ) : (
                    <>
                      <Save className="mr-2 h-4 w-4" />
                      Save Changes
                    </>
                  )}
                </Button>
              </CardFooter>
            </Card>
          </TabsContent>

          <TabsContent value="seo" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Meta Information</CardTitle>
                <CardDescription>SEO settings for search engines</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid gap-2">
                  <Label htmlFor="metaTitle">Meta Title</Label>
                  <Input
                    id="metaTitle"
                    value={metaTitle}
                    onChange={(e) => setMetaTitle(e.target.value)}
                    placeholder="Enter meta title"
                  />
                </div>
                <div className="grid gap-2">
                  <Label htmlFor="metaDescription">Meta Description</Label>
                  <Textarea
                    id="metaDescription"
                    value={metaDescription}
                    onChange={(e) => setMetaDescription(e.target.value)}
                    placeholder="Enter meta description"
                    rows={3}
                  />
                </div>
                <div className="grid gap-2">
                  <Label htmlFor="metaKeywords">Meta Keywords</Label>
                  <div className="flex flex-wrap gap-2 mb-2">
                    {metaKeywords.map((keyword, index) => (
                      <Badge key={index} variant="secondary" className="gap-1">
                        {keyword}
                        <button
                          type="button"
                          onClick={() => removeKeyword(keyword)}
                          className="ml-1 rounded-full outline-none focus:ring-2 focus:ring-offset-2"
                        >
                          <Trash2 className="h-3 w-3" />
                        </button>
                      </Badge>
                    ))}
                  </div>
                  <div className="flex gap-2">
                    <Input
                      id="keywordInput"
                      value={keywordInput}
                      onChange={(e) => setKeywordInput(e.target.value)}
                      placeholder="Add keyword"
                      onKeyDown={(e) => {
                        if (e.key === 'Enter') {
                          e.preventDefault();
                          addKeyword();
                        }
                      }}
                    />
                    <Button type="button" onClick={addKeyword} variant="outline">
                      <Tag className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </CardContent>
              <CardFooter>
                <Button
                  onClick={handleSaveSettings}
                  disabled={isSaving}
                >
                  {isSaving ? (
                    <>
                      <motion.div
                        className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent"
                      />
                      Saving...
                    </>
                  ) : (
                    <>
                      <Save className="mr-2 h-4 w-4" />
                      Save Changes
                    </>
                  )}
                </Button>
              </CardFooter>
            </Card>
          </TabsContent>

          <TabsContent value="notifications">
            <Card>
              <CardHeader>
                <CardTitle>Notification Settings</CardTitle>
                <CardDescription>Manage how you receive notifications</CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                {isLoading ? (
                  <div className="flex items-center justify-center h-40">
                    <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-[rgb(var(--primary))]"></div>
                  </div>
                ) : (
                  <div className="space-y-4">
                    <div className="flex items-center space-x-2">
                      <Switch
                        id="email-notifications"
                        checked={emailNotifications}
                        onCheckedChange={setEmailNotifications}
                      />
                      <div className="grid gap-1.5">
                        <Label htmlFor="email-notifications">Email Notifications</Label>
                        <p className="text-sm text-adaptive-muted">
                          Receive email notifications for important updates
                        </p>
                      </div>
                    </div>

                    <div className="border-t pt-4">
                      <Label>Notification Types</Label>
                      <div className="space-y-3 mt-3">
                        <div className="flex items-center space-x-2">
                          <Switch id="user-signup" defaultChecked />
                          <Label htmlFor="user-signup">New user signups</Label>
                        </div>

                        <div className="flex items-center space-x-2">
                          <Switch id="tool-usage" defaultChecked />
                          <Label htmlFor="tool-usage">Tool usage reports</Label>
                        </div>

                        <div className="flex items-center space-x-2">
                          <Switch id="system-updates" defaultChecked />
                          <Label htmlFor="system-updates">System updates</Label>
                        </div>

                        <div className="flex items-center space-x-2">
                          <Switch id="security-alerts" defaultChecked />
                          <Label htmlFor="security-alerts">Security alerts</Label>
                        </div>
                      </div>
                    </div>

                    <div className="flex justify-end">
                      <Button
                        onClick={handleSaveNotifications}
                        className="btn-primary"
                        disabled={isLoading || isSaving}
                      >
                        {isSaving ? (
                          <>
                            <span className="animate-spin mr-2">⟳</span>
                            Saving...
                          </>
                        ) : (
                          <>
                            <Bell className="h-4 w-4 mr-2" />
                            Save Notification Settings
                          </>
                        )}
                      </Button>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </DashboardLayout>
  );
}
