import { NextRequest, NextResponse } from "next/server";
import connectToDatabase from "@/lib/db";
import bcrypt from "bcryptjs";
// import User from "@/models/User";
import mongoose from "mongoose";

// This is a development-only endpoint to reset a user's password
// It should be disabled in production

export async function POST(req: NextRequest) {
  // Only allow in development mode
  if (process.env.NODE_ENV !== "development") {
    return NextResponse.json(
      { error: "This endpoint is only available in development mode" },
      { status: 403 }
    );
  }

  try {
    const { email, newPassword } = await req.json();

    if (!email || !newPassword) {
      return NextResponse.json(
        { error: "Email and new password are required" },
        { status: 400 }
      );
    }

    console.log(`Attempting to reset password for user: ${email}`);

    // Connect to database
    await connectToDatabase();
    console.log("Connected to database");

    // Get the users collection
    const usersCollection = mongoose.connection.collection("users");

    // Find the user
    // const user = await User.findOne({ email }).select("+password");
    const user = await usersCollection.findOne({ email });

    if (!user) {
      console.log(`User not found with email: ${email}`);
      return NextResponse.json(
        { error: "User not found" },
        { status: 404 }
      );
    }

    console.log(`User found: ${user.name}, resetting password...`);

    // Hash the new password
    console.log(`Original password: ${newPassword}`);
    console.log(`Original password length: ${newPassword.length}`);
    const salt = await bcrypt.genSalt(10);
    const hashedPassword = await bcrypt.hash(newPassword, salt);
    console.log(`New hashed password: ${hashedPassword}`);
    console.log(`New hashed password length: ${hashedPassword.length}`);
    console.log("New password hashed successfully");

    // Update the user's password
    await usersCollection.updateOne(
      { _id: user._id },
      {
        $set: {
          password: hashedPassword,
          updatedAt: new Date()
        }
      }
    );

    console.log(`Password reset successful for user: ${email}`);

    return NextResponse.json({
      success: true,
      message: "Password reset successful",
      user: {
        id: user._id,
        name: user.name,
        email: user.email,
      },
      newCredentials: {
        email: email,
        password: newPassword
      }
    });

  } catch (error: any) {
    console.error("Password reset error:", error);
    return NextResponse.json(
      {
        success: false,
        error: "Password reset failed",
        details: process.env.NODE_ENV === "development" ? error.message : undefined
      },
      { status: 500 }
    );
  }
}
