"use client"

import Link from "next/link";
import { FiArrowRight } from "react-icons/fi";
import { motion } from "framer-motion";

interface ToolCardProps {
  title: string;
  description: string;
  icon: string;
  href: string;
  inputFormat?: string;
  outputFormat?: string;
  category: string;
  delay?: number;
  // componentName:string;
}

export default function ToolCard({
  title,
  description,
  icon,
  href,
  inputFormat,
  outputFormat,
  category,
  // componentName,
  delay = 0,
}: ToolCardProps) {
  const categoryColors = {
    pdf: 'bg-blue-100 text-blue-800',
    office: 'bg-purple-100 text-purple-800',
    image: 'bg-green-100 text-green-800'
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3, delay }}
      whileHover={{ y: -5 }}
    >
      <Link
        href={href}
        className="group relative block h-full p-6 bg-white rounded-xl border border-gray-200 hover:border-blue-500 transition-all shadow-sm hover:shadow-md overflow-hidden"
      >
        <div className="flex items-start justify-between mb-4">
          <span className="text-3xl" aria-hidden="true">
            {icon}
          </span>
          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${categoryColors[category as keyof typeof categoryColors]}`}>
            {category}
          </span>
        </div>

        <h3 className="text-lg font-semibold text-gray-900 mb-2 group-hover:text-blue-600 transition-colors">
          {title}
        </h3>
        <p className="text-gray-600 mb-4">{description}</p>

        <div className="flex justify-between items-center mt-auto">
          <div className="inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-gray-100 text-gray-800">
            {inputFormat} → {outputFormat}
          </div>
          <FiArrowRight className="w-5 h-5 text-gray-400 group-hover:text-blue-500 group-hover:translate-x-1 transition-all" />
        </div>

        <div className="absolute inset-0 -z-10 bg-gradient-to-br from-blue-50/50 to-white/50 opacity-0 group-hover:opacity-100 transition-opacity" />
      </Link>
    </motion.div>
  );
}