import HtmlToPdfConverter from "@/components/tools/converters/HtmlToPdfConverter";
import ToolLayout from "@/components/tools/ToolLayout";
import { Metadata } from "next";

export const metadata: Metadata = {
  title: "HTML to PDF Converter - PDF Tools",
  description: "Convert HTML pages or code to PDF documents online",
  keywords: "HTML to PDF, convert HTML, webpage to PDF, HTML code to PDF",
};

export default function HtmlToPdfPage() {
  return (
    <ToolLayout
      title="HTML to PDF Converter"
      icon="🌐"
      description="Convert HTML pages or code to PDF documents"
    >
      <HtmlToPdfConverter />
    </ToolLayout>
  );
}
