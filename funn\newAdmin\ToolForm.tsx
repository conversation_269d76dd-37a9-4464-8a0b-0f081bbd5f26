import React, { useState } from "react";
import { Button } from "@/components/ui/Button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import {
  <PERSON>,
  <PERSON><PERSON>onte<PERSON>,
  <PERSON><PERSON><PERSON>er,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
} from "@/components/ui/card";
// import { ITool } from "@/models/Tool";

interface ToolFormProps {
  initialData?: {
    _id?: string;
    title: string;
    description: string;
    category: string;
    icon: string;
    inputFormat: string;
    outputFormat: string;
    componentName: string;
  };
  onSubmit: (data: any) => void;
  onCancel: () => void;
}

const ToolForm: React.FC<ToolFormProps> = ({
  initialData = {
    title: "",
    description: "",
    category: "",
    icon: "",
    inputFormat: "",
    outputFormat: "",
    componentName: "",
  },
  onSubmit,
  onCancel,
}) => {
  const [formData, setFormData] = useState(initialData);
  const [errors, setErrors] = useState<Record<string, string>>({});

  const handleChange = (
    e: React.ChangeEvent<
      HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement
    >,
  ) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));

    // Clear error when field is edited
    if (errors[name]) {
      setErrors((prev) => {
        const newErrors = { ...prev };
        delete newErrors[name];
        return newErrors;
      });
    }
  };

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.title.trim()) newErrors.title = "Title is required";
    if (!formData.description.trim())
      newErrors.description = "Description is required";
    if (!formData.category.trim()) newErrors.category = "Category is required";
    if (!formData.icon.trim()) newErrors.icon = "Icon is required";
    if (!formData.componentName.trim())
      newErrors.componentName = "Component name is required";

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    if (validateForm()) {
      onSubmit(formData);
    }
  };

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>
          {initialData._id ? "Edit Tool" : "Create New Tool"}
        </CardTitle>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="space-y-2">
            <label htmlFor="title" className="text-sm font-medium">
              Title
            </label>
            <Input
              id="title"
              name="title"
              value={formData.title}
              onChange={handleChange}
              placeholder="PDF Compressor"
              className={errors.title ? "border-red-500" : ""}
            />
            {errors.title && (
              <p className="text-xs text-red-500">{errors.title}</p>
            )}
          </div>

          <div className="space-y-2">
            <label htmlFor="description" className="text-sm font-medium">
              Description
            </label>
            <Textarea
              id="description"
              name="description"
              value={formData.description}
              onChange={handleChange}
              placeholder="Compress PDF files to reduce file size"
              className={errors.description ? "border-red-500" : ""}
              rows={3}
            />
            {errors.description && (
              <p className="text-xs text-red-500">{errors.description}</p>
            )}
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <label htmlFor="category" className="text-sm font-medium">
                Category
              </label>
              <Input
                id="category"
                name="category"
                value={formData.category}
                onChange={handleChange}
                placeholder="PDF Tools"
                className={errors.category ? "border-red-500" : ""}
              />
              {errors.category && (
                <p className="text-xs text-red-500">{errors.category}</p>
              )}
            </div>

            <div className="space-y-2">
              <label htmlFor="icon" className="text-sm font-medium">
                Icon (Lucide Icon Name)
              </label>
              <Input
                id="icon"
                name="icon"
                value={formData.icon}
                onChange={handleChange}
                placeholder="FileText"
                className={errors.icon ? "border-red-500" : ""}
              />
              {errors.icon && (
                <p className="text-xs text-red-500">{errors.icon}</p>
              )}
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <label htmlFor="inputFormat" className="text-sm font-medium">
                Input Format
              </label>
              <Input
                id="inputFormat"
                name="inputFormat"
                value={formData.inputFormat}
                onChange={handleChange}
                placeholder="PDF"
                className={errors.inputFormat ? "border-red-500" : ""}
              />
              {errors.inputFormat && (
                <p className="text-xs text-red-500">{errors.inputFormat}</p>
              )}
            </div>

            <div className="space-y-2">
              <label htmlFor="outputFormat" className="text-sm font-medium">
                Output Format
              </label>
              <Input
                id="outputFormat"
                name="outputFormat"
                value={formData.outputFormat}
                onChange={handleChange}
                placeholder="PDF"
                className={errors.outputFormat ? "border-red-500" : ""}
              />
              {errors.outputFormat && (
                <p className="text-xs text-red-500">{errors.outputFormat}</p>
              )}
            </div>
          </div>

          <div className="space-y-2">
            <label htmlFor="componentName" className="text-sm font-medium">
              Component Name
            </label>
            <Input
              id="componentName"
              name="componentName"
              value={formData.componentName}
              onChange={handleChange}
              placeholder="PdfCompressor"
              className={errors.componentName ? "border-red-500" : ""}
            />
            {errors.componentName && (
              <p className="text-xs text-red-500">{errors.componentName}</p>
            )}
          </div>
        </form>
      </CardContent>
      <CardFooter className="flex justify-between">
        <Button variant="outline" onClick={onCancel}>
          Cancel
        </Button>
        <Button onClick={handleSubmit}>
          {initialData._id ? "Update Tool" : "Create Tool"}
        </Button>
      </CardFooter>
    </Card>
  );
};

export default ToolForm;
