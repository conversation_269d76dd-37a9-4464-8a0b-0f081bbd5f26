"use client";

import { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { <PERSON><PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  <PERSON>hart,
  Pie,
  Cell,
  Legend,
  LineChart,
  Line,
  AreaChart,
  Area
} from 'recharts';
import {
  Loader2,
  RefreshCw,
  BarChart2,
  TrendingUp,
  Clock,
  Download,
  Activity,
  ArrowUpRight,
  ArrowDownRight,
  Users,
  FileText,
  Eye
} from "lucide-react";
import { toast } from "@/hooks/use-toast";
import { RequireRole } from "@/components/auth/RequireRole";

// Define types for our analytics data
interface AnalyticsSummary {
  totalUsers: number;
  totalPosts: number;
  totalTools: number;
  totalViews: number;
  userGrowth: number;
  postGrowth: number;
  toolGrowth: number;
  viewGrowth: number;
}

interface TimeSeriesData {
  date: string;
  users?: number;
  posts?: number;
  views?: number;
  toolUses?: number;
}

interface AnalyticsData {
  summary: AnalyticsSummary;
  timeSeriesData: TimeSeriesData[];
  popularTools: { name: string; value: number; color: string }[];
  popularPosts: { name: string; value: number; color: string }[];
  userDevices: { name: string; value: number; color: string }[];
}

export default function AnalyticsPage() {
  const [timeRange, setTimeRange] = useState('month');
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [analyticsData, setAnalyticsData] = useState<AnalyticsData | null>(null);
  const [activeTab, setActiveTab] = useState('overview');

  // Function to fetch analytics data
  const fetchAnalyticsData = async (range: string) => {
    try {
      setIsLoading(true);
      setError(null);

      // Fetch summary data
      const summaryResponse = await fetch(`/api/analytics?type=summary&range=${range}`);
      if (!summaryResponse.ok) {
        throw new Error(`Failed to fetch summary data: ${summaryResponse.status}`);
      }
      const summaryData = await summaryResponse.json();

      // Fetch traffic data
      const trafficResponse = await fetch(`/api/analytics?type=traffic&range=${range}`);
      if (!trafficResponse.ok) {
        throw new Error(`Failed to fetch traffic data: ${trafficResponse.status}`);
      }
      const trafficData = await trafficResponse.json();

      // Fetch tool usage data
      const toolsResponse = await fetch(`/api/analytics?type=tools&range=${range}`);
      if (!toolsResponse.ok) {
        throw new Error(`Failed to fetch tools data: ${toolsResponse.status}`);
      }
      const toolsData = await toolsResponse.json();

      // Fetch content data
      const contentResponse = await fetch(`/api/analytics?type=content&range=${range}`);
      if (!contentResponse.ok) {
        throw new Error(`Failed to fetch content data: ${contentResponse.status}`);
      }
      const contentData = await contentResponse.json();

      // Fetch user device data
      const usersResponse = await fetch(`/api/analytics?type=users&range=${range}`);
      if (!usersResponse.ok) {
        throw new Error(`Failed to fetch users data: ${usersResponse.status}`);
      }
      const usersData = await usersResponse.json();

      // Combine all data
      setAnalyticsData({
        summary: summaryData.summary || {
          totalUsers: 0,
          totalPosts: 0,
          totalTools: 0,
          totalViews: 0,
          userGrowth: 0,
          postGrowth: 0,
          toolGrowth: 0,
          viewGrowth: 0
        },
        timeSeriesData: trafficData.timeSeriesData || [],
        popularTools: toolsData.toolUsage || [],
        popularPosts: contentData.popularPosts || [],
        userDevices: usersData.userDevices || []
      });

      setIsLoading(false);
    } catch (err) {
      console.error("Error fetching analytics:", err);
      setError("Failed to fetch analytics data");
      setIsLoading(false);
    }
  };

  // Fetch data when time range changes
  useEffect(() => {
    fetchAnalyticsData(timeRange);
  }, [timeRange]);

  const handleRefresh = () => {
    fetchAnalyticsData(timeRange);
    toast({
      title: "Refreshing analytics",
      description: "Fetching the latest analytics data...",
    });
  };

  return (
    <RequireRole role="admin">
      <div className="space-y-6">
        <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
          <h1 className="text-2xl font-bold">Analytics Dashboard</h1>
          <div className="flex items-center gap-2">
            <Select value={timeRange} onValueChange={setTimeRange}>
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="Select time range" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="day">Last 24 Hours</SelectItem>
                <SelectItem value="week">Last 7 Days</SelectItem>
                <SelectItem value="month">Last 30 Days</SelectItem>
                <SelectItem value="quarter">Last Quarter</SelectItem>
                <SelectItem value="year">Last Year</SelectItem>
              </SelectContent>
            </Select>

            <Button variant="outline" onClick={handleRefresh}>
              <RefreshCw className="mr-2 h-4 w-4" />
              Refresh
            </Button>
          </div>
        </div>

        {isLoading ? (
          <div className="flex items-center justify-center h-[60vh]">
            <div className="text-center">
              <Loader2 className="h-12 w-12 animate-spin mx-auto" />
              <p className="mt-4 text-lg text-muted-foreground">Loading analytics data...</p>
            </div>
          </div>
        ) : error ? (
          <Card>
            <CardHeader className="flex justify-between items-center">
              <div>
                <CardTitle>Error</CardTitle>
              </div>
              <Button onClick={handleRefresh}>
                <RefreshCw className="mr-2 h-4 w-4" />
                Retry
              </Button>
            </CardHeader>
            <CardContent className="flex items-center justify-center h-[40vh]">
              <div className="text-center">
                <p className="text-lg text-destructive">Failed to load analytics data</p>
                <p className="mt-2 text-muted-foreground">Please try again later or contact support</p>
              </div>
            </CardContent>
          </Card>
        ) : (
          <>
            {/* Summary Cards */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              <Card>
                <CardContent className="pt-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm text-muted-foreground">Total Users</p>
                      <div className="text-2xl font-bold">
                        {analyticsData?.summary.totalUsers.toLocaleString()}
                      </div>
                    </div>
                    <div className="p-2 bg-primary/10 rounded-full">
                      <Users className="h-5 w-5 text-primary" />
                    </div>
                  </div>
                  <div className={`text-xs mt-2 flex items-center ${
                    analyticsData?.summary.userGrowth && analyticsData.summary.userGrowth > 0
                      ? 'text-green-600'
                      : 'text-red-600'
                  }`}>
                    {analyticsData?.summary.userGrowth && analyticsData.summary.userGrowth > 0
                      ? <ArrowUpRight className="h-3 w-3 mr-1" />
                      : <ArrowDownRight className="h-3 w-3 mr-1" />
                    }
                    <span>
                      {analyticsData?.summary.userGrowth
                        ? `${Math.abs(analyticsData.summary.userGrowth).toFixed(1)}% from last ${timeRange}`
                        : `No change from last ${timeRange}`
                      }
                    </span>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="pt-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm text-muted-foreground">Blog Posts</p>
                      <div className="text-2xl font-bold">
                        {analyticsData?.summary.totalPosts.toLocaleString()}
                      </div>
                    </div>
                    <div className="p-2 bg-primary/10 rounded-full">
                      <FileText className="h-5 w-5 text-primary" />
                    </div>
                  </div>
                  <div className={`text-xs mt-2 flex items-center ${
                    analyticsData?.summary.postGrowth && analyticsData.summary.postGrowth > 0
                      ? 'text-green-600'
                      : 'text-red-600'
                  }`}>
                    {analyticsData?.summary.postGrowth && analyticsData.summary.postGrowth > 0
                      ? <ArrowUpRight className="h-3 w-3 mr-1" />
                      : <ArrowDownRight className="h-3 w-3 mr-1" />
                    }
                    <span>
                      {analyticsData?.summary.postGrowth
                        ? `${Math.abs(analyticsData.summary.postGrowth).toFixed(1)}% from last ${timeRange}`
                        : `No change from last ${timeRange}`
                      }
                    </span>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="pt-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm text-muted-foreground">Total Tools</p>
                      <div className="text-2xl font-bold">
                        {analyticsData?.summary.totalTools.toLocaleString()}
                      </div>
                    </div>
                    <div className="p-2 bg-primary/10 rounded-full">
                      <BarChart2 className="h-5 w-5 text-primary" />
                    </div>
                  </div>
                  <div className={`text-xs mt-2 flex items-center ${
                    analyticsData?.summary.toolGrowth && analyticsData.summary.toolGrowth > 0
                      ? 'text-green-600'
                      : 'text-red-600'
                  }`}>
                    {analyticsData?.summary.toolGrowth && analyticsData.summary.toolGrowth > 0
                      ? <ArrowUpRight className="h-3 w-3 mr-1" />
                      : <ArrowDownRight className="h-3 w-3 mr-1" />
                    }
                    <span>
                      {analyticsData?.summary.toolGrowth
                        ? `${Math.abs(analyticsData.summary.toolGrowth).toFixed(1)}% from last ${timeRange}`
                        : `No change from last ${timeRange}`
                      }
                    </span>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="pt-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm text-muted-foreground">Page Views</p>
                      <div className="text-2xl font-bold">
                        {analyticsData?.summary.totalViews.toLocaleString()}
                      </div>
                    </div>
                    <div className="p-2 bg-primary/10 rounded-full">
                      <Eye className="h-5 w-5 text-primary" />
                    </div>
                  </div>
                  <div className={`text-xs mt-2 flex items-center ${
                    analyticsData?.summary.viewGrowth && analyticsData.summary.viewGrowth > 0
                      ? 'text-green-600'
                      : 'text-red-600'
                  }`}>
                    {analyticsData?.summary.viewGrowth && analyticsData.summary.viewGrowth > 0
                      ? <ArrowUpRight className="h-3 w-3 mr-1" />
                      : <ArrowDownRight className="h-3 w-3 mr-1" />
                    }
                    <span>
                      {analyticsData?.summary.viewGrowth
                        ? `${Math.abs(analyticsData.summary.viewGrowth).toFixed(1)}% from last ${timeRange}`
                        : `No change from last ${timeRange}`
                      }
                    </span>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Tabs for different analytics views */}
            <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
              <TabsList>
                <TabsTrigger value="overview">Overview</TabsTrigger>
                <TabsTrigger value="tools">Tools</TabsTrigger>
                <TabsTrigger value="content">Content</TabsTrigger>
                <TabsTrigger value="users">Users</TabsTrigger>
              </TabsList>

              <TabsContent value="overview">
                <Card>
                  <CardHeader>
                    <CardTitle>Site Activity</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="h-[400px]">
                      {analyticsData.timeSeriesData && analyticsData.timeSeriesData.length > 0 ? (
                        <ResponsiveContainer width="100%" height="100%">
                          <LineChart
                            data={analyticsData.timeSeriesData}
                            margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
                          >
                            <CartesianGrid strokeDasharray="3 3" />
                            <XAxis dataKey="date" />
                            <YAxis />
                            <Tooltip />
                            <Legend />
                            <Line type="monotone" dataKey="views" stroke="#8884d8" name="Page Views" />
                            <Line type="monotone" dataKey="toolUses" stroke="#82ca9d" name="Tool Uses" />
                          </LineChart>
                        </ResponsiveContainer>
                      ) : (
                        <div className="flex items-center justify-center h-full">
                          <p className="text-muted-foreground">No data available for this time period</p>
                        </div>
                      )}
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="tools">
                <Card>
                  <CardHeader>
                    <CardTitle>Popular Tools</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="h-[400px]">
                      {analyticsData.popularTools && analyticsData.popularTools.length > 0 ? (
                        <ResponsiveContainer width="100%" height="100%">
                          <PieChart>
                            <Pie
                              data={analyticsData.popularTools}
                              cx="50%"
                              cy="50%"
                              labelLine={false}
                              outerRadius={150}
                              fill="#8884d8"
                              dataKey="value"
                              label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                            >
                              {analyticsData.popularTools.map((entry, index) => (
                                <Cell key={`cell-${index}`} fill={entry.color} />
                              ))}
                            </Pie>
                            <Tooltip />
                            <Legend />
                          </PieChart>
                        </ResponsiveContainer>
                      ) : (
                        <div className="flex items-center justify-center h-full">
                          <p className="text-muted-foreground">No data available for this time period</p>
                        </div>
                      )}
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="content">
                <Card>
                  <CardHeader>
                    <CardTitle>Popular Content</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="h-[400px]">
                      {analyticsData.popularPosts && analyticsData.popularPosts.length > 0 ? (
                        <ResponsiveContainer width="100%" height="100%">
                          <BarChart
                            data={analyticsData.popularPosts}
                            layout="vertical"
                            margin={{ top: 20, right: 30, left: 100, bottom: 5 }}
                          >
                            <CartesianGrid strokeDasharray="3 3" />
                            <XAxis type="number" />
                            <YAxis dataKey="name" type="category" />
                            <Tooltip />
                            <Legend />
                            <Bar dataKey="value" name="Views" fill="#8884d8" />
                          </BarChart>
                        </ResponsiveContainer>
                      ) : (
                        <div className="flex items-center justify-center h-full">
                          <p className="text-muted-foreground">No data available for this time period</p>
                        </div>
                      )}
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="users">
                <Card>
                  <CardHeader>
                    <CardTitle>User Devices</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="h-[400px]">
                      {analyticsData.userDevices && analyticsData.userDevices.length > 0 ? (
                        <ResponsiveContainer width="100%" height="100%">
                          <PieChart>
                            <Pie
                              data={analyticsData.userDevices}
                              cx="50%"
                              cy="50%"
                              labelLine={false}
                              outerRadius={150}
                              fill="#8884d8"
                              dataKey="value"
                              label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                            >
                              {analyticsData.userDevices.map((entry, index) => (
                                <Cell key={`cell-${index}`} fill={entry.color} />
                              ))}
                            </Pie>
                            <Tooltip />
                            <Legend />
                          </PieChart>
                        </ResponsiveContainer>
                      ) : (
                        <div className="flex items-center justify-center h-full">
                          <p className="text-muted-foreground">No data available for this time period</p>
                        </div>
                      )}
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>
            </Tabs>
          </>
        )}
      </div>
    </RequireRole>
  );
}
