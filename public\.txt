
01:fix this error 
[{
	"resource": "/C:/Users/<USER>/Desktop/ToolBox/version/src/components/layout/Header.tsx",
	"owner": "typescript",
	"message": "Property 'catch' does not exist on type 'void'.",
}]

02:header signup button redirect signup route not exist don't change button name and anything just replace signup route to register
03:signup and login form input active color not visible i can't see any word 
04:login form i need some better design like signup is very good and add password eye toggle visible or not and implement forget route and functionalities 
05:  fix this error    ⚠ Fast Refresh had to perform a full reload. Read more: https://nextjs.org/docs/messages/fast-refresh-reload
 GET /_next/static/webpack/78ee464c3f521d46.webpack.hot-update.json 404 in 5383ms
 06: GET /api/analytics?type=summary&range=month 400 in 61ms i need proper fetch data 
 07: remove subcription with email from blog page button component everything remove related subcription 
 08:remove blog stats page from admin panel, and Home in sidebar
 09:remove complete comments from blog page and admin page in sidebar delete comments page from admin panel


  create SEO Settings Admin Panel UI:
Prompt:

"Design a modern, clean SEO Settings admin dashboard page for a website. The layout should include:

🔧 A vertical sidebar on the left with navigation items: 'General', 'SEO Settings', 'Appearance', 'Integrations'.

📄 On the main page area, include these SEO fields:

Site Title

Site Description

Meta Keywords

Default Share Image (upload input with image preview)

Robots.txt settings (toggle switch: Allow / Disallow)

Canonical URL input

Open Graph Title & Description

Twitter Card settings

Favicon uploader

Sitemap toggle (Enable/Disable)

Google Analytics ID field

🔴 The header should say “SEO Settings”, with save and reset buttons at the top-right.

🎨 Style should be professional, flat UI, with cards, soft shadows, rounded corners, and real inputs/text.
Sticky top header bar with solid background (not transparent).
Show tooltips or small help texts beside complex fields.

💻 Web dashboard view, light mode."

🛠️ Optional Enhancements:
Include a live preview snippet for how title/description would look in Google.

Add a tab switcher for Basic vs Advanced SEO.

Mobile-responsive layout with drawer sidebar.

