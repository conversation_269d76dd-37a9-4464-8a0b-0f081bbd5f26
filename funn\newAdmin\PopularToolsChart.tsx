import React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import {
  <PERSON><PERSON><PERSON>,
  Bar,
  XAxis,
  <PERSON>A<PERSON>s,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
} from "recharts";

interface PopularToolsChartProps {
  data: { name: string; visits: number }[];
  loading?: boolean;
}

const PopularToolsChart: React.FC<PopularToolsChartProps> = ({
  data = [
    { name: "PDF Compressor", visits: 1245 },
    { name: "Image Editor", visits: 932 },
    { name: "PDF Converter", visits: 621 },
    { name: "Text Analyzer", visits: 453 },
    { name: "Video Compressor", visits: 287 },
  ],
  loading = false,
}) => {
  return (
    <Card className="col-span-2">
      <CardHeader>
        <CardTitle>Popular Tools</CardTitle>
      </CardHeader>
      <CardContent>
        {loading ? (
          <div className="h-[300px] flex items-center justify-center">
            Loading chart data...
          </div>
        ) : data.length > 0 ? (
          <ResponsiveContainer width="100%" height={300}>
            <BarChart data={data}>
              <CartesianGrid strokeDasharray="3 3" opacity={0.2} />
              <XAxis
                dataKey="name"
                tick={{ fontSize: 12 }}
                angle={-45}
                textAnchor="end"
              />
              <YAxis tick={{ fontSize: 12 }} />
              <Tooltip
                contentStyle={{
                  backgroundColor: "var(--card)",
                  borderColor: "var(--border)",
                  borderRadius: "0.5rem",
                  boxShadow: "0 4px 6px -1px rgba(0, 0, 0, 0.1)",
                }}
                labelStyle={{ fontWeight: "bold" }}
              />
              <Bar
                dataKey="visits"
                fill="var(--primary)"
                radius={[4, 4, 0, 0]}
                name="Usage Count"
              />
            </BarChart>
          </ResponsiveContainer>
        ) : (
          <div className="h-[300px] flex items-center justify-center text-muted-foreground">
            No tool usage data available yet
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default PopularToolsChart;
