// import React, { useEffect } from "react";
// import { useNavigate } from "react-router-dom";
// import Sidebar from "./Sidebar";
// import { useAuth } from "@/contexts/AuthContext";
// import { useRouter } from "next/navigation";
// // import { ThemeToggle } from "../theme/ThemeToggle";

// interface AdminLayoutProps {
//   children: React.ReactNode;
//   title: string;
// }

// const AdminLayout: React.FC<AdminLayoutProps> = ({ children, title }) => {
// //   const { logout, checkIsAdmin } = useAuth();
//   const { user, isAdmin, loading, logout } = useAuth();
//   const router = useRouter();

//   useEffect(() => {
//     // If the user is not an admin, redirect to the home page
//     if (!isAdmin) {
//       router.push("/"); // Use next/navigation for redirection
//     }
//   }, [isAdmin, router]);

//   if (loading) {
//     return <div>Loading...</div>;
//   }

//   return (
//     <div className="flex h-screen bg-gray-100 dark:bg-gray-900">
//       <Sidebar onLogout={logout} />

//       <div className="flex-1 overflow-auto">
//         <header className="bg-white dark:bg-gray-800 shadow-sm">
//           <div className="px-6 py-4 flex justify-between items-center">
//             <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
//               {title}
//             </h1>
//             {/* <ThemeToggle /> */}
//           </div>
//         </header>

//         <main className="p-6">{children}</main>
//       </div>
//     </div>
//   );
// };

// export default AdminLayout;
