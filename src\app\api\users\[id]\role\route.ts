import { NextRequest, NextResponse } from "next/server";
import connectToDatabase from "@/lib/db";
import User from "@/models/User";
import { canModifyUserRole } from "@/lib/auth/permissions";

// API endpoint to update a user's role
export async function PATCH(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Get user ID and role from request headers (set by middleware)
    const currentUserId = request.headers.get("x-user-id");
    const currentUserRole = request.headers.get("x-user-role");

    if (!currentUserId) {
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 }
      );
    }

    // Parse the request body
    const body = await request.json();
    const { role } = body;

    // Validate the role
    if (!["admin", "user"].includes(role)) {
      return NextResponse.json(
        { error: "Invalid role" },
        { status: 400 }
      );
    }

    // Connect to the database
    await connectToDatabase();

    // Get the target user
    const targetUser = await User.findById(params.id);

    if (!targetUser) {
      return NextResponse.json(
        { error: "User not found" },
        { status: 404 }
      );
    }

    // Check if the target user is protected
    if (targetUser.isProtected) {
      return NextResponse.json(
        { error: "This user's role cannot be modified" },
        { status: 403 }
      );
    }

    // Check if the current user has permission to modify the target user's role
    const canModify = canModifyUserRole(
      currentUserId,
      currentUserRole,
      targetUser._id.toString(),
      targetUser.role,
      role
    );

    if (!canModify) {
      return NextResponse.json(
        { error: "You don't have permission to modify this user's role" },
        { status: 403 }
      );
    }

    // Update the user's role
    targetUser.role = role;
    await targetUser.save();

    // Return the updated user
    const updatedUser = targetUser.toObject();
    delete updatedUser.password;

    return NextResponse.json(updatedUser);
  } catch (error) {
    console.error("PATCH /api/users/[id]/role error:", error);
    return NextResponse.json(
      { error: "Failed to update user role" },
      { status: 500 }
    );
  }
}
