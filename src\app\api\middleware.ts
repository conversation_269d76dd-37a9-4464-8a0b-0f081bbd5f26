import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';
import { getToken } from 'next-auth/jwt';

const secret = process.env.NEXTAUTH_SECRET;

export async function middleware(request: NextRequest) {
  // Get the token from the request
  const token = await getToken({ req: request, secret });

  // Clone the request headers
  const requestHeaders = new Headers(request.headers);

  // If token exists, set user ID and role in headers
  if (token) {
    requestHeaders.set('x-user-id', token.sub as string);
    requestHeaders.set('x-user-role', token.role as string || 'user');
  }

  // Return the response with the modified headers
  return NextResponse.next({
    request: {
      headers: requestHeaders,
    },
  });
}

// Apply middleware to all API routes
export const config = {
  matcher: ['/api/:path*'],
};
