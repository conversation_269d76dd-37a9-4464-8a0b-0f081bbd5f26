import { Metadata } from "next";
import { notFound } from "next/navigation";
import GenericConverter from "@/components/tools/GenericConverter";
import { toolConfigs } from "@/lib/tool-configs";
import ToolLayout from "@/components/tools/ToolLayout";

type Props = {
  params: { tool: string };
};

export async function generateMetadata({ params }: Props): Promise<Metadata> {
  const toolConfig = toolConfigs[params.tool];

  if (!toolConfig) {
    return {
      title: "Tool Not Found - PDF Tools",
    };
  }

  return {
    title: `${toolConfig.title.replace("How to ", "")} - PDF Tools`,
    description: toolConfig.description,
    keywords: `PDF tools, ${params.tool.replace("-", " ")}, convert PDF, edit PDF`,
  };
}

export default function ToolPage({ params }: Props) {
  const toolConfig = toolConfigs[params.tool];

  if (!toolConfig) {
    notFound();
  }

  // Get the icon from the tool name
  const getIconForTool = (toolName: string) => {
    const iconMap: Record<string, string> = {
      "pdf-to-word": "📄",
      "pdf-to-powerpoint": "📊",
      "pdf-to-excel": "📈",
      "pdf-to-jpg": "🖼️",
      "pdf-to-pdfa": "📑",
      "word-to-pdf": "📝",
      "powerpoint-to-pdf": "🎯",
      "excel-to-pdf": "📊",
      "jpg-to-pdf": "📸",
      "html-to-pdf": "🌐",
      "merge-pdf": "🔗",
      "split-pdf": "✂️",
      "compress-pdf": "🗜️",
      "rotate-pdf": "🔄",
    };
    return iconMap[toolName] || "📁";
  };

  return (
    <ToolLayout
      title={toolConfig.title.replace("How to ", "")}
      description={toolConfig.description}
      icon={getIconForTool(params.tool)}
    >
      <GenericConverter {...toolConfig} />
    </ToolLayout>
  );
}
