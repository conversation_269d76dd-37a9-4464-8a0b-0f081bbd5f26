"use client";

import { useState, useEffect } from "react";
import DashboardLayout from "@/components/admin/DashboardLayout";
import { Card, CardContent, CardDescription, CardHeader, CardTitle, CardFooter } from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { Separator } from "@/components/ui/separator";
import { Button } from "@/components/ui/button";
import { useTheme } from "@/hooks/useTheme";
import { motion } from "framer-motion";
import {
  Moon,
  Sun,
  Monitor,
  Palette,
  Check,
  Laptop,
  SunMoon
} from "lucide-react";

export default function ThemePage() {
  const { theme, toggleTheme, setTheme } = useTheme();
  const [mounted, setMounted] = useState(false);

  // Ensure component is mounted before rendering to avoid hydration mismatch
  useEffect(() => {
    setMounted(true);
  }, []);

  if (!mounted) {
    return null;
  }

  return (
    <DashboardLayout title="Theme Settings" subtitle="Customize the appearance of your admin panel">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3 }}
        >
          <Card>
            <CardHeader>
              <CardTitle>Theme Mode</CardTitle>
              <CardDescription>Choose between light and dark mode</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-3 gap-4">
                <Card
                  className={`cursor-pointer border-2 transition-all ${
                    theme === 'light'
                      ? 'border-[rgb(var(--primary))]'
                      : 'border-[rgb(var(--border))]'
                  }`}
                  onClick={() => setTheme('light')}
                >
                  <CardContent className="p-4 flex flex-col items-center justify-center text-center">
                    <div className="h-12 w-12 rounded-full bg-[#f8f9fa] border border-[#dee2e6] flex items-center justify-center mb-2">
                      <Sun className="h-6 w-6 text-[#212529]" />
                    </div>
                    <p className="font-medium">Light</p>
                    {theme === 'light' && (
                      <div className="absolute top-2 right-2">
                        <Check className="h-4 w-4 text-[rgb(var(--primary))]" />
                      </div>
                    )}
                  </CardContent>
                </Card>

                <Card
                  className={`cursor-pointer border-2 transition-all ${
                    theme === 'dark'
                      ? 'border-[rgb(var(--primary))]'
                      : 'border-[rgb(var(--border))]'
                  }`}
                  onClick={() => setTheme('dark')}
                >
                  <CardContent className="p-4 flex flex-col items-center justify-center text-center">
                    <div className="h-12 w-12 rounded-full bg-[#212529] border border-[#495057] flex items-center justify-center mb-2">
                      <Moon className="h-6 w-6 text-[#f8f9fa]" />
                    </div>
                    <p className="font-medium">Dark</p>
                    {theme === 'dark' && (
                      <div className="absolute top-2 right-2">
                        <Check className="h-4 w-4 text-[rgb(var(--primary))]" />
                      </div>
                    )}
                  </CardContent>
                </Card>

                <Card
                  className={`cursor-pointer border-2 transition-all ${
                    theme === 'system'
                      ? 'border-[rgb(var(--primary))]'
                      : 'border-[rgb(var(--border))]'
                  }`}
                  onClick={() => setTheme('system')}
                >
                  <CardContent className="p-4 flex flex-col items-center justify-center text-center">
                    <div className="h-12 w-12 rounded-full bg-gradient-to-r from-[#f8f9fa] to-[#212529] border border-[#dee2e6] flex items-center justify-center mb-2">
                      <Monitor className="h-6 w-6 text-adaptive" />
                    </div>
                    <p className="font-medium">System</p>
                    {theme === 'system' && (
                      <div className="absolute top-2 right-2">
                        <Check className="h-4 w-4 text-[rgb(var(--primary))]" />
                      </div>
                    )}
                  </CardContent>
                </Card>
              </div>

              <Separator />

              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label htmlFor="auto-switch">Automatic Dark Mode</Label>
                    <p className="text-sm text-adaptive-muted">
                      Automatically switch between light and dark mode based on your system preferences
                    </p>
                  </div>
                  <Switch
                    id="auto-switch"
                    checked={theme === 'system'}
                    onCheckedChange={(checked) => setTheme(checked ? 'system' : 'light')}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label htmlFor="toggle-switch">Quick Toggle</Label>
                    <p className="text-sm text-adaptive-muted">
                      Toggle between light and dark mode with a single click
                    </p>
                  </div>
                  <Button
                    variant="outline"
                    size="icon"
                    onClick={toggleTheme}
                  >
                    {theme === 'dark' ? <Sun className="h-5 w-5" /> : <Moon className="h-5 w-5" />}
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: 0.1 }}
        >
          <Card>
            <CardHeader>
              <CardTitle>Preview</CardTitle>
              <CardDescription>See how your theme looks</CardDescription>
            </CardHeader>
            <CardContent>
              <div className={`rounded-lg border p-4 ${
                theme === 'dark' ? 'bg-[#121212] text-white' : 'bg-white text-[#121212]'
              }`}>
                <div className="flex items-center justify-between mb-4">
                  <div className="flex items-center gap-2">
                    <div className={`h-8 w-8 rounded-md ${
                      theme === 'dark' ? 'bg-blue-500' : 'bg-blue-600'
                    } flex items-center justify-center text-white`}>
                      <Palette className="h-5 w-5" />
                    </div>
                    <h3 className="font-bold">Theme Preview</h3>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className={`h-6 w-6 rounded-full ${
                      theme === 'dark' ? 'bg-gray-700' : 'bg-gray-200'
                    } flex items-center justify-center`}>
                      <SunMoon className={`h-4 w-4 ${
                        theme === 'dark' ? 'text-gray-300' : 'text-gray-700'
                      }`} />
                    </div>
                    <div className={`h-6 w-6 rounded-full ${
                      theme === 'dark' ? 'bg-gray-700' : 'bg-gray-200'
                    } flex items-center justify-center`}>
                      <Laptop className={`h-4 w-4 ${
                        theme === 'dark' ? 'text-gray-300' : 'text-gray-700'
                      }`} />
                    </div>
                  </div>
                </div>

                <div className={`rounded-md ${
                  theme === 'dark' ? 'bg-gray-800' : 'bg-gray-100'
                } p-3 mb-4`}>
                  <p className={`text-sm ${
                    theme === 'dark' ? 'text-gray-300' : 'text-gray-700'
                  }`}>
                    This is how your content will appear in {theme} mode.
                  </p>
                </div>

                <div className="flex gap-2">
                  <button className={`px-3 py-1.5 rounded-md text-sm ${
                    theme === 'dark'
                      ? 'bg-blue-600 text-white'
                      : 'bg-blue-500 text-white'
                  }`}>
                    Primary Button
                  </button>
                  <button className={`px-3 py-1.5 rounded-md text-sm ${
                    theme === 'dark'
                      ? 'bg-gray-700 text-gray-200'
                      : 'bg-gray-200 text-gray-700'
                  }`}>
                    Secondary Button
                  </button>
                </div>
              </div>
            </CardContent>
            <CardFooter>
              <p className="text-sm text-adaptive-muted">
                Your theme preference is saved automatically and will be applied across all admin pages.
              </p>
            </CardFooter>
          </Card>
        </motion.div>
      </div>
    </DashboardLayout>
  );
}

